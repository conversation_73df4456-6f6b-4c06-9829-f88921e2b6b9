<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="description" content="Modern SVG Editor - Edit, customize, and export SVG graphics with ease">
    <meta name="keywords" content="SVG editor, vector graphics, design tool, SVG customization">
    <meta name="author" content="Jermesa Studio">
    <title>SVG Editor - Jermesa Studio</title>
    
    <!-- Google Fonts - Open SIL License -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&family=JetBrains+Mono:wght@400;500&display=swap" rel="stylesheet">
    
    <link rel="stylesheet" href="styles.css">
</head>
<body>
    <!-- <PERSON><PERSON> Banner -->
    <div id="cookieConsent" class="cookie-consent">
        <div class="cookie-content">
            <p>We use cookies to enhance your experience. By continuing to visit this site you agree to our use of cookies.</p>
            <div class="cookie-actions">
                <button id="acceptCookies" class="btn btn-primary">Accept</button>
                <a href="https://jermesa.com/privacy-policy/" target="_blank" class="privacy-link">Privacy Policy</a>
            </div>
        </div>
    </div>

    <!-- Main Application -->
    <div class="app-container">
        <!-- Header -->
        <header class="app-header">
            <div class="header-content">
                <h1 class="app-title">
                    <span class="icon-wrapper">
                        <svg viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                            <path d="M11 4H4a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7"/>
                            <path d="M18.5 2.5a2.121 2.121 0 0 1 3 3L12 15l-4 1 1-4 9.5-9.5z"/>
                        </svg>
                    </span>
                    SVG Editor
                </h1>
                <div class="header-actions">
                    <button id="exportBtn" class="btn btn-secondary">
                        <span class="icon">
                            <svg viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                <path d="M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4"/>
                                <polyline points="7,10 12,15 17,10"/>
                                <line x1="12" x2="12" y1="15" y2="3"/>
                            </svg>
                        </span>
                        Export
                    </button>
                </div>
            </div>
        </header>

        <!-- Main Content -->
        <main class="app-main">
            <!-- Input Section -->
            <section class="input-section">
                <div class="section-header">
                    <h2>Input SVG</h2>
                    <div class="input-tabs">
                        <button class="tab-btn active" data-tab="text">Text Input</button>
                        <button class="tab-btn" data-tab="file">File Upload</button>
                    </div>
                </div>
                
                <div class="input-content">
                    <!-- Text Input Tab -->
                    <div id="textTab" class="tab-content active">
                        <textarea id="svgInput" placeholder="Paste your SVG code here..." rows="8"></textarea>
                        <div class="input-actions">
                            <button id="loadSampleBtn" class="btn btn-outline">Load Sample</button>
                            <button id="clearInputBtn" class="btn btn-outline">Clear</button>
                        </div>
                    </div>
                    
                    <!-- File Upload Tab -->
                    <div id="fileTab" class="tab-content">
                        <div class="file-upload-area" id="fileUploadArea">
                            <div class="upload-content">
                                <span class="upload-icon">
                                    <svg viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                        <path d="M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4"/>
                                        <polyline points="17,8 12,3 7,8"/>
                                        <line x1="12" x2="12" y1="3" y2="15"/>
                                    </svg>
                                </span>
                                <p>Drop SVG file here or <span class="upload-link">browse</span></p>
                                <input type="file" id="fileInput" accept=".svg" hidden>
                            </div>
                        </div>
                    </div>
                </div>
            </section>

            <!-- Preview and Editor Section -->
            <section class="editor-section">
                <div class="editor-layout">
                    <!-- SVG Preview -->
                    <div class="preview-panel">
                        <div class="panel-header">
                            <h3>Preview</h3>
                            <div class="preview-controls">
                                <button id="zoomInBtn" class="btn btn-icon" title="Zoom In">
                                    <svg viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                        <circle cx="11" cy="11" r="8"/>
                                        <path d="m21 21-4.35-4.35"/>
                                        <line x1="11" x2="11" y1="8" y2="14"/>
                                        <line x1="8" x2="14" y1="11" y2="11"/>
                                    </svg>
                                </button>
                                <button id="zoomOutBtn" class="btn btn-icon" title="Zoom Out">
                                    <svg viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                        <circle cx="11" cy="11" r="8"/>
                                        <path d="m21 21-4.35-4.35"/>
                                        <line x1="8" x2="14" y1="11" y2="11"/>
                                    </svg>
                                </button>
                                <button id="resetZoomBtn" class="btn btn-icon" title="Reset Zoom">
                                    <svg viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                        <path d="M3 12a9 9 0 0 1 9-9 9.75 9.75 0 0 1 6.74 2.74L21 8"/>
                                        <path d="M21 3v5h-5"/>
                                        <path d="M21 12a9 9 0 0 1-9 9 9.75 9.75 0 0 1-6.74-2.74L3 16"/>
                                        <path d="M8 16H3v5"/>
                                    </svg>
                                </button>
                            </div>
                        </div>
                        <div class="preview-container" id="previewContainer">
                            <div class="preview-placeholder">
                                <span class="placeholder-icon">
                                    <svg viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                        <rect width="18" height="18" x="3" y="3" rx="2" ry="2"/>
                                        <circle cx="9" cy="9" r="2"/>
                                        <path d="m21 15-3.086-3.086a2 2 0 0 0-2.828 0L6 21"/>
                                    </svg>
                                </span>
                                <p>SVG preview will appear here</p>
                            </div>
                        </div>
                    </div>

                    <!-- Editing Controls -->
                    <div class="controls-panel">
                        <div class="panel-header">
                            <h3>Edit Elements</h3>
                            <button id="randomizeColorsBtn" class="btn btn-primary">
                                <span class="icon">
                                    <svg viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                        <path d="M3 12a9 9 0 0 1 9-9 9.75 9.75 0 0 1 6.74 2.74L21 8"/>
                                        <path d="M21 3v5h-5"/>
                                        <path d="M21 12a9 9 0 0 1-9 9 9.75 9.75 0 0 1-6.74-2.74L3 16"/>
                                        <path d="M8 16H3v5"/>
                                    </svg>
                                </span>
                                Randomize Colors
                            </button>
                        </div>
                        <div class="controls-content" id="controlsContent">
                            <div class="no-elements">
                                <p>Load an SVG to start editing</p>
                            </div>
                        </div>
                    </div>
                </div>
            </section>
        </main>

        <!-- Footer -->
        <footer class="app-footer">
            <div class="footer-content">
                <div class="footer-left">
                    <p>Created by <a href="https://www.jermesa.com" target="_blank" rel="noopener">Jermesa Studio</a></p>
                </div>
                <div class="footer-right">
                    <p>Fonts: <a href="https://fonts.google.com/specimen/Inter" target="_blank" rel="noopener">Inter</a> & <a href="https://fonts.google.com/specimen/JetBrains+Mono" target="_blank" rel="noopener">JetBrains Mono</a> (Open SIL License)</p>
                    <a href="https://jermesa.com/privacy-policy/" target="_blank" rel="noopener">Privacy Policy</a>
                </div>
            </div>
        </footer>
    </div>

    <!-- Export Modal -->
    <div id="exportModal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h3>Export SVG</h3>
                <button class="modal-close" id="closeExportModal">
                    <svg viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                        <path d="m18 6-12 12"/>
                        <path d="m6 6 12 12"/>
                    </svg>
                </button>
            </div>
            <div class="modal-body">
                <div class="export-options">
                    <button id="downloadSvgBtn" class="btn btn-primary">
                        <span class="icon">
                            <svg viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                <path d="M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4"/>
                                <polyline points="7,10 12,15 17,10"/>
                                <line x1="12" x2="12" y1="15" y2="3"/>
                            </svg>
                        </span>
                        Download SVG File
                    </button>
                    <button id="copySvgBtn" class="btn btn-secondary">
                        <span class="icon">
                            <svg viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                <rect width="14" height="14" x="8" y="8" rx="2" ry="2"/>
                                <path d="M4 16c-1.1 0-2-.9-2-2V4c0-1.1.9-2 2-2h10c1.1 0 2 .9 2 2"/>
                            </svg>
                        </span>
                        Copy to Clipboard
                    </button>
                </div>
                <div class="export-code">
                    <label for="exportTextarea">SVG Code:</label>
                    <textarea id="exportTextarea" readonly rows="10"></textarea>
                </div>
            </div>
        </div>
    </div>

    <script src="script.js"></script>
</body>
</html>
