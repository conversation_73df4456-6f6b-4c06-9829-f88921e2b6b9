# SVG Editor - Jermesa Studio

A modern, responsive SVG editor web application that allows users to edit, customize, and export SVG graphics with ease.

## Features

### Core Functionality
- **SVG Input Methods:**
  - Direct text input for SVG code
  - File upload functionality for .svg files
  - Real-time preview of SVG content

- **Editing Capabilities:**
  - Automatic detection and listing of all SVG elements
  - Individual element controls for:
    - Stroke color and thickness
    - Fill color
    - Opacity
  - Auto-detection of all colors used in the SVG
  - Color picker for each detected color
  - 25 professional color presets
  - "Randomize Colors" feature for multi-colored SVG elements

- **Export Options:**
  - Download as .svg file
  - Copy SVG code to clipboard
  - Export SVG code as text

### Technical Features
- Modern, clean design aesthetic
- Fully responsive design (mobile-first approach)
- Cross-browser compatibility
- Smooth interactions and transitions
- Error handling for file uploads and SVG parsing
- Cookie consent implementation

## Usage

1. **Load an SVG:**
   - Use the "Text Input" tab to paste SVG code directly
   - Use the "File Upload" tab to upload an .svg file
   - Click "Load Sample" to try the editor with a sample SVG

2. **Edit the SVG:**
   - View detected colors and change them using color pickers
   - Expand individual elements to customize their properties
   - Use color presets for quick styling
   - Click "Randomize Colors" for instant color variations

3. **Preview and Export:**
   - View real-time preview with zoom controls
   - Use the Export button to download or copy the edited SVG

## File Structure

```
jermesa_svg_editor/
├── index.html          # Main HTML file
├── styles.css          # CSS styles and responsive design
├── script.js           # JavaScript functionality
├── icons/
│   └── icons-data.json # Icon data (used internally)
└── README.md           # This file
```

## Browser Compatibility

- Chrome 60+
- Firefox 55+
- Safari 12+
- Edge 79+

## Technologies Used

- HTML5
- CSS3 (Grid, Flexbox, Custom Properties)
- Vanilla JavaScript (ES6+)
- Google Fonts (Inter & JetBrains Mono - Open SIL License)

## Legal & Attribution

- **Created by:** [Jermesa Studio](https://www.jermesa.com)
- **Fonts:** Inter & JetBrains Mono (Open SIL License)
- **Privacy Policy:** [https://jermesa.com/privacy-policy/](https://jermesa.com/privacy-policy/)

## License

This project is created by Jermesa Studio. Please refer to the privacy policy for terms of use.

## Development

To run the application locally:

1. Clone or download the project files
2. Open `index.html` in a modern web browser
3. No build process or server required - it's a static web application

## Features in Detail

### SVG Parsing
- Validates SVG syntax and structure
- Extracts all visual elements (paths, shapes, text, etc.)
- Detects colors in various formats (hex, rgb, hsl, named colors)
- Handles gradients and complex SVG structures

### Color Management
- Automatic color detection from all SVG attributes
- Real-time color replacement throughout the SVG
- Professional color presets for modern design
- Color picker integration for precise color selection

### Responsive Design
- Mobile-first CSS approach
- Adaptive layout for tablets and desktops
- Touch-friendly interface elements
- Optimized for various screen sizes

### Error Handling
- File type validation for uploads
- SVG syntax validation
- User-friendly error messages
- Graceful fallbacks for unsupported features

## Testing

To test the application functionality:

1. Open `index.html` in a web browser
2. Click "Load Sample" to load a test SVG
3. Test the following features:
   - **Element Controls**: Expand each element and try changing fill color, stroke color, stroke width, and opacity
   - **Color Detection**: Use the detected colors section to change colors globally
   - **Color Presets**: Click on preset colors to apply them
   - **Randomize Colors**: Click the "Randomize Colors" button
   - **Export**: Use the Export button to download or copy the SVG
   - **File Upload**: Try uploading your own SVG files

### Known Issues Fixed

- ✅ **Element controls not working**: Fixed DOM reference issues when updating SVG elements
- ✅ **Missing elements in export**: Fixed SVG structure preservation during edits
- ✅ **Color picker synchronization**: Improved color handling and validation

## Contributing

This is a project by Jermesa Studio. For questions or suggestions, please visit [jermesa.com](https://www.jermesa.com).
