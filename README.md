# SVG Editor - Jermesa Studio

A modern, responsive SVG editor web application that allows users to edit, customize, and export SVG graphics with ease.

## Features

### Core Functionality
- **SVG Input Methods:**
  - Direct text input for SVG code
  - File upload functionality for .svg files
  - Real-time preview of SVG content

- **Editing Capabilities:**
  - Automatic detection and listing of all SVG elements
  - Individual element controls for:
    - Stroke color and thickness
    - Fill color
    - Opacity
  - Auto-detection of all colors used in the SVG
  - Color picker for each detected color
  - 25 professional color presets
  - "Randomize Colors" feature for multi-colored SVG elements

- **Animation System:**
  - Individual element animations with 20+ presets
  - Whole SVG animations
  - Animation timing controls (duration, delay, iterations, direction)
  - Animation categories: movement, scale, rotation, entrance, fade, special
  - Random animation generator
  - Animation timeline and playback controls
  - CSS and JavaScript animation export

- **Export Options:**
  - Download as .svg file (static or animated)
  - Copy SVG code to clipboard
  - Export SVG code as text
  - Animated SVG export with CSS animations
  - Animation preview functionality

### Technical Features
- Modern, clean design aesthetic
- Fully responsive design (mobile-first approach)
- Cross-browser compatibility
- Smooth interactions and transitions
- Error handling for file uploads and SVG parsing
- Cookie consent implementation

## Usage

1. **Load an SVG:**
   - Use the "Text Input" tab to paste SVG code directly
   - Use the "File Upload" tab to upload an .svg file
   - Click "Load Sample" to try the editor with a sample SVG

2. **Edit the SVG:**
   - **Styling Tab:**
     - View detected colors and change them using color pickers
     - Expand individual elements to customize their properties
     - Use color presets for quick styling
     - Click "Randomize Colors" for instant color variations
   - **Animation Tab:**
     - Apply animations to individual elements
     - Set global animations for the entire SVG
     - Customize timing, duration, and iterations
     - Use animation presets organized by category
     - Click "Random All" for instant animated effects

3. **Preview and Export:**
   - View real-time preview with zoom controls
   - Play, pause, and stop animations using playback controls
   - Use the Export button to download or copy the SVG
   - Choose between static or animated SVG export
   - Preview animations in a separate window

## File Structure

```
jermesa_svg_editor/
├── index.html          # Main HTML file
├── styles.css          # CSS styles and responsive design
├── script.js           # JavaScript functionality
├── icons/
│   └── icons-data.json # Icon data (used internally)
└── README.md           # This file
```

## Browser Compatibility

- Chrome 60+
- Firefox 55+
- Safari 12+
- Edge 79+

## Technologies Used

- HTML5
- CSS3 (Grid, Flexbox, Custom Properties)
- Vanilla JavaScript (ES6+)
- Google Fonts (Inter & JetBrains Mono - Open SIL License)

## Legal & Attribution

- **Created by:** [Jermesa Studio](https://www.jermesa.com)
- **Fonts:** Inter & JetBrains Mono (Open SIL License)
- **Privacy Policy:** [https://jermesa.com/privacy-policy/](https://jermesa.com/privacy-policy/)

## License

This project is created by Jermesa Studio. Please refer to the privacy policy for terms of use.

## Development

To run the application locally:

1. Clone or download the project files
2. Open `index.html` in a modern web browser
3. No build process or server required - it's a static web application

## Features in Detail

### SVG Parsing
- Validates SVG syntax and structure
- Extracts all visual elements (paths, shapes, text, etc.)
- Detects colors in various formats (hex, rgb, hsl, named colors)
- Handles gradients and complex SVG structures

### Color Management
- Automatic color detection from all SVG attributes
- Real-time color replacement throughout the SVG
- Professional color presets for modern design
- Color picker integration for precise color selection

### Animation System
- **20+ Animation Presets** organized by category:
  - Movement: bounce, shake, wobble, slide animations
  - Scale: pulse, heartbeat, zoom in/out, rubber band
  - Rotation: spin, swing, flip
  - Entrance: slide in from all directions
  - Fade: fade in/out, flash
  - Special: jello, rubber band effects
- **Individual Element Animation**: Apply different animations to each SVG element
- **Global SVG Animation**: Animate the entire SVG as one unit
- **Timing Controls**: Duration, delay, iterations, direction
- **Random Animation Generator**: Apply random animations to all elements
- **Animation Timeline**: Visual timeline showing all active animations
- **Playback Controls**: Play, pause, stop animations
- **Export Options**: Export as CSS-animated SVG or static SVG

### Responsive Design
- Mobile-first CSS approach
- Adaptive layout for tablets and desktops
- Touch-friendly interface elements
- Optimized for various screen sizes

### Error Handling
- File type validation for uploads
- SVG syntax validation
- User-friendly error messages
- Graceful fallbacks for unsupported features

## Testing

To test the application functionality:

1. Open `index.html` in a web browser
2. Click "Load Sample" to load a test SVG
3. Test the following features:
   - **Styling Controls**:
     - Expand each element and try changing fill color, stroke color, stroke width, and opacity
     - Use the detected colors section to change colors globally
     - Click on preset colors to apply them
     - Click the "Randomize Colors" button
   - **Animation Controls**:
     - Switch to the Animation tab
     - Apply animations to individual elements using preset buttons
     - Set global animations for the entire SVG
     - Use the playback controls (play, pause, stop)
     - Try the "Random All" button for instant animated effects
     - Adjust timing settings (duration, delay, iterations)
   - **Export**:
     - Use the Export button to download or copy the SVG
     - Try both static and animated export options
     - Use the animation preview feature
   - **File Upload**: Try uploading your own SVG files

### Known Issues Fixed

- ✅ **Element controls not working**: Fixed DOM reference issues when updating SVG elements
- ✅ **Missing elements in export**: Fixed SVG structure preservation during edits
- ✅ **Color picker synchronization**: Improved color handling and validation

## Contributing

This is a project by Jermesa Studio. For questions or suggestions, please visit [jermesa.com](https://www.jermesa.com).
