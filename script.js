// SVG Editor Application
class SVGEditor {
    constructor() {
        this.currentSVG = null;
        this.svgElements = [];
        this.detectedColors = new Set();
        this.colorPresets = [
            '#6366f1', '#8b5cf6', '#ec4899', '#ef4444', '#f97316',
            '#f59e0b', '#eab308', '#84cc16', '#22c55e', '#10b981',
            '#06b6d4', '#0ea5e9', '#3b82f6', '#6366f1', '#8b5cf6',
            '#a855f7', '#d946ef', '#ec4899', '#f43f5e', '#ef4444',
            '#f97316', '#f59e0b', '#eab308', '#84cc16', '#22c55e'
        ];
        
        this.init();
    }

    init() {
        this.setupEventListeners();
        this.setupCookieConsent();
        this.loadSampleSVG();
    }

    setupEventListeners() {
        // Tab switching
        document.querySelectorAll('.tab-btn').forEach(btn => {
            btn.addEventListener('click', (e) => this.switchTab(e.target.dataset.tab));
        });

        // SVG input
        const svgInput = document.getElementById('svgInput');
        svgInput.addEventListener('input', () => this.handleSVGInput(svgInput.value));

        // File upload
        const fileInput = document.getElementById('fileInput');
        const fileUploadArea = document.getElementById('fileUploadArea');
        
        fileInput.addEventListener('change', (e) => this.handleFileUpload(e.target.files[0]));
        fileUploadArea.addEventListener('click', () => fileInput.click());
        fileUploadArea.addEventListener('dragover', (e) => this.handleDragOver(e));
        fileUploadArea.addEventListener('drop', (e) => this.handleFileDrop(e));

        // Control buttons
        document.getElementById('loadSampleBtn').addEventListener('click', () => this.loadSampleSVG());
        document.getElementById('clearInputBtn').addEventListener('click', () => this.clearInput());
        document.getElementById('randomizeColorsBtn').addEventListener('click', () => this.randomizeColors());

        // Preview controls
        document.getElementById('zoomInBtn').addEventListener('click', () => this.zoomPreview(1.2));
        document.getElementById('zoomOutBtn').addEventListener('click', () => this.zoomPreview(0.8));
        document.getElementById('resetZoomBtn').addEventListener('click', () => this.resetZoom());

        // Export functionality
        document.getElementById('exportBtn').addEventListener('click', () => this.showExportModal());
        document.getElementById('closeExportModal').addEventListener('click', () => this.hideExportModal());
        document.getElementById('downloadSvgBtn').addEventListener('click', () => this.downloadSVG());
        document.getElementById('copySvgBtn').addEventListener('click', () => this.copySVGToClipboard());

        // Modal close on backdrop click
        document.getElementById('exportModal').addEventListener('click', (e) => {
            if (e.target.id === 'exportModal') this.hideExportModal();
        });
    }

    setupCookieConsent() {
        const cookieConsent = document.getElementById('cookieConsent');
        const acceptBtn = document.getElementById('acceptCookies');
        
        // Check if cookies were already accepted
        if (!localStorage.getItem('cookiesAccepted')) {
            setTimeout(() => {
                cookieConsent.classList.add('show');
            }, 1000);
        }

        acceptBtn.addEventListener('click', () => {
            localStorage.setItem('cookiesAccepted', 'true');
            cookieConsent.classList.remove('show');
        });
    }

    switchTab(tabName) {
        // Update tab buttons
        document.querySelectorAll('.tab-btn').forEach(btn => {
            btn.classList.toggle('active', btn.dataset.tab === tabName);
        });

        // Update tab content
        document.querySelectorAll('.tab-content').forEach(content => {
            content.classList.toggle('active', content.id === `${tabName}Tab`);
        });
    }

    handleSVGInput(svgCode) {
        if (svgCode.trim()) {
            this.processSVG(svgCode);
        } else {
            this.clearPreview();
        }
    }

    handleFileUpload(file) {
        if (!file) return;

        if (file.type !== 'image/svg+xml' && !file.name.endsWith('.svg')) {
            this.showError('Please select a valid SVG file.');
            return;
        }

        const reader = new FileReader();
        reader.onload = (e) => {
            const svgCode = e.target.result;
            document.getElementById('svgInput').value = svgCode;
            this.processSVG(svgCode);
            this.switchTab('text'); // Switch to text tab to show the loaded code
        };
        reader.onerror = () => {
            this.showError('Error reading the file.');
        };
        reader.readAsText(file);
    }

    handleDragOver(e) {
        e.preventDefault();
        e.stopPropagation();
        e.currentTarget.classList.add('dragover');
    }

    handleFileDrop(e) {
        e.preventDefault();
        e.stopPropagation();
        e.currentTarget.classList.remove('dragover');
        
        const files = e.dataTransfer.files;
        if (files.length > 0) {
            this.handleFileUpload(files[0]);
        }
    }

    processSVG(svgCode) {
        try {
            // Clean and validate SVG
            const cleanSVG = this.cleanSVGCode(svgCode);
            
            // Create DOM parser to validate SVG
            const parser = new DOMParser();
            const doc = parser.parseFromString(cleanSVG, 'image/svg+xml');
            
            // Check for parsing errors
            const parserError = doc.querySelector('parsererror');
            if (parserError) {
                throw new Error('Invalid SVG format');
            }

            const svgElement = doc.querySelector('svg');
            if (!svgElement) {
                throw new Error('No SVG element found');
            }

            this.currentSVG = cleanSVG;
            this.updatePreview(cleanSVG);
            this.parseSVGElements(svgElement);
            this.detectColors(svgElement);
            this.updateControlsPanel();
            
        } catch (error) {
            this.showError(`Error processing SVG: ${error.message}`);
            this.clearPreview();
        }
    }

    cleanSVGCode(svgCode) {
        // Remove any XML declarations and DOCTYPE
        let cleaned = svgCode.replace(/<\?xml[^>]*\?>/gi, '');
        cleaned = cleaned.replace(/<!DOCTYPE[^>]*>/gi, '');
        cleaned = cleaned.trim();
        
        // Ensure SVG has proper namespace if missing
        if (cleaned.includes('<svg') && !cleaned.includes('xmlns=')) {
            cleaned = cleaned.replace('<svg', '<svg xmlns="http://www.w3.org/2000/svg"');
        }
        
        return cleaned;
    }

    updatePreview(svgCode) {
        const previewContainer = document.getElementById('previewContainer');
        previewContainer.innerHTML = svgCode;
        
        const svgElement = previewContainer.querySelector('svg');
        if (svgElement) {
            // Ensure SVG is responsive
            svgElement.style.maxWidth = '100%';
            svgElement.style.height = 'auto';
            
            // Add viewBox if missing
            if (!svgElement.getAttribute('viewBox') && svgElement.getAttribute('width') && svgElement.getAttribute('height')) {
                const width = svgElement.getAttribute('width');
                const height = svgElement.getAttribute('height');
                svgElement.setAttribute('viewBox', `0 0 ${width} ${height}`);
            }
        }
    }

    clearPreview() {
        const previewContainer = document.getElementById('previewContainer');
        previewContainer.innerHTML = `
            <div class="preview-placeholder">
                <span class="placeholder-icon">
                    <svg viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                        <rect width="18" height="18" x="3" y="3" rx="2" ry="2"/>
                        <circle cx="9" cy="9" r="2"/>
                        <path d="m21 15-3.086-3.086a2 2 0 0 0-2.828 0L6 21"/>
                    </svg>
                </span>
                <p>SVG preview will appear here</p>
            </div>
        `;
        this.currentSVG = null;
        this.svgElements = [];
        this.detectedColors.clear();
        this.updateControlsPanel();
    }

    parseSVGElements(svgElement) {
        this.svgElements = [];
        const elements = svgElement.querySelectorAll('*');
        
        elements.forEach((element, index) => {
            const tagName = element.tagName.toLowerCase();
            
            // Skip non-visual elements
            if (['defs', 'metadata', 'title', 'desc', 'style', 'script'].includes(tagName)) {
                return;
            }

            const elementData = {
                id: `element-${index}`,
                element: element,
                tagName: tagName,
                fill: element.getAttribute('fill') || element.style.fill || 'none',
                stroke: element.getAttribute('stroke') || element.style.stroke || 'none',
                strokeWidth: element.getAttribute('stroke-width') || element.style.strokeWidth || '1',
                opacity: element.getAttribute('opacity') || element.style.opacity || '1'
            };

            this.svgElements.push(elementData);
        });
    }

    detectColors(svgElement) {
        this.detectedColors.clear();
        
        // Find all color attributes
        const colorAttributes = ['fill', 'stroke', 'stop-color', 'flood-color', 'lighting-color'];
        const elements = svgElement.querySelectorAll('*');
        
        elements.forEach(element => {
            colorAttributes.forEach(attr => {
                const value = element.getAttribute(attr) || element.style[attr];
                if (value && value !== 'none' && value !== 'transparent') {
                    // Extract color values (hex, rgb, hsl, named colors)
                    const colorMatch = value.match(/#[0-9a-f]{3,6}|rgb\([^)]+\)|hsl\([^)]+\)|[a-z]+/gi);
                    if (colorMatch) {
                        colorMatch.forEach(color => {
                            if (color.startsWith('#') || color.startsWith('rgb') || color.startsWith('hsl')) {
                                this.detectedColors.add(color);
                            }
                        });
                    }
                }
            });
        });
    }

    updateControlsPanel() {
        const controlsContent = document.getElementById('controlsContent');
        
        if (this.svgElements.length === 0) {
            controlsContent.innerHTML = `
                <div class="no-elements">
                    <p>Load an SVG to start editing</p>
                </div>
            `;
            return;
        }

        let html = '';
        
        // Add detected colors section
        if (this.detectedColors.size > 0) {
            html += this.generateColorSection();
        }
        
        // Add elements section
        html += this.generateElementsSection();
        
        controlsContent.innerHTML = html;
        this.attachControlEventListeners();
    }

    generateColorSection() {
        const colors = Array.from(this.detectedColors);
        
        let html = `
            <div class="control-section">
                <h4>Detected Colors</h4>
                <div class="color-grid">
        `;
        
        colors.forEach((color, index) => {
            html += `
                <div class="color-item">
                    <input type="color" id="color-${index}" value="${this.normalizeColor(color)}" data-original="${color}">
                    <label for="color-${index}">${color}</label>
                </div>
            `;
        });
        
        html += `
                </div>
                <div class="color-presets">
                    <h5>Color Presets</h5>
                    <div class="preset-grid">
        `;
        
        this.colorPresets.forEach((color, index) => {
            html += `
                <button class="preset-color" data-color="${color}" style="background-color: ${color}" title="${color}"></button>
            `;
        });
        
        html += `
                    </div>
                </div>
            </div>
        `;
        
        return html;
    }

    generateElementsSection() {
        let html = `
            <div class="control-section">
                <h4>SVG Elements (${this.svgElements.length})</h4>
                <div class="elements-list">
        `;
        
        this.svgElements.forEach((elementData, index) => {
            html += `
                <div class="element-item" data-element-id="${elementData.id}">
                    <div class="element-header">
                        <span class="element-tag">&lt;${elementData.tagName}&gt;</span>
                        <button class="element-toggle" data-target="controls-${index}">
                            <svg viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                <path d="m6 9 6 6 6-6"/>
                            </svg>
                        </button>
                    </div>
                    <div class="element-controls" id="controls-${index}">
                        <div class="control-group">
                            <label>Fill Color:</label>
                            <input type="color" class="element-fill" value="${this.normalizeColor(elementData.fill)}" data-element="${index}">
                        </div>
                        <div class="control-group">
                            <label>Stroke Color:</label>
                            <input type="color" class="element-stroke" value="${this.normalizeColor(elementData.stroke)}" data-element="${index}">
                        </div>
                        <div class="control-group">
                            <label>Stroke Width:</label>
                            <input type="range" class="element-stroke-width" min="0" max="10" step="0.5" value="${elementData.strokeWidth}" data-element="${index}">
                            <span class="range-value">${elementData.strokeWidth}</span>
                        </div>
                        <div class="control-group">
                            <label>Opacity:</label>
                            <input type="range" class="element-opacity" min="0" max="1" step="0.1" value="${elementData.opacity}" data-element="${index}">
                            <span class="range-value">${elementData.opacity}</span>
                        </div>
                    </div>
                </div>
            `;
        });
        
        html += `
                </div>
            </div>
        `;
        
        return html;
    }

    normalizeColor(color) {
        if (!color || color === 'none' || color === 'transparent') {
            return '#000000';
        }
        
        // If it's already a hex color, return it
        if (color.startsWith('#')) {
            return color.length === 4 ? color + color.slice(1) : color; // Convert #abc to #aabbcc
        }
        
        // For other color formats, we'll use a temporary element to get computed color
        const tempElement = document.createElement('div');
        tempElement.style.color = color;
        document.body.appendChild(tempElement);
        const computedColor = window.getComputedStyle(tempElement).color;
        document.body.removeChild(tempElement);
        
        // Convert rgb to hex
        const rgbMatch = computedColor.match(/rgb\((\d+),\s*(\d+),\s*(\d+)\)/);
        if (rgbMatch) {
            const r = parseInt(rgbMatch[1]).toString(16).padStart(2, '0');
            const g = parseInt(rgbMatch[2]).toString(16).padStart(2, '0');
            const b = parseInt(rgbMatch[3]).toString(16).padStart(2, '0');
            return `#${r}${g}${b}`;
        }
        
        return '#000000';
    }

    attachControlEventListeners() {
        // Color change listeners
        document.querySelectorAll('input[type="color"]').forEach(input => {
            input.addEventListener('change', (e) => this.handleColorChange(e));
        });
        
        // Range input listeners
        document.querySelectorAll('input[type="range"]').forEach(input => {
            input.addEventListener('input', (e) => this.handleRangeChange(e));
        });
        
        // Element toggle listeners
        document.querySelectorAll('.element-toggle').forEach(button => {
            button.addEventListener('click', (e) => this.toggleElementControls(e));
        });
        
        // Preset color listeners
        document.querySelectorAll('.preset-color').forEach(button => {
            button.addEventListener('click', (e) => this.applyPresetColor(e));
        });
    }

    handleColorChange(e) {
        const input = e.target;
        const newColor = input.value;
        
        if (input.dataset.original) {
            // This is a detected color input
            this.replaceColorInSVG(input.dataset.original, newColor);
        } else if (input.dataset.element !== undefined) {
            // This is an element-specific color input
            const elementIndex = parseInt(input.dataset.element);
            const elementData = this.svgElements[elementIndex];
            
            if (input.classList.contains('element-fill')) {
                elementData.element.setAttribute('fill', newColor);
                elementData.fill = newColor;
            } else if (input.classList.contains('element-stroke')) {
                elementData.element.setAttribute('stroke', newColor);
                elementData.stroke = newColor;
            }
            
            this.updateCurrentSVG();
        }
    }

    handleRangeChange(e) {
        const input = e.target;
        const value = input.value;
        const elementIndex = parseInt(input.dataset.element);
        const elementData = this.svgElements[elementIndex];
        
        // Update the display value
        const valueSpan = input.nextElementSibling;
        if (valueSpan && valueSpan.classList.contains('range-value')) {
            valueSpan.textContent = value;
        }
        
        if (input.classList.contains('element-stroke-width')) {
            elementData.element.setAttribute('stroke-width', value);
            elementData.strokeWidth = value;
        } else if (input.classList.contains('element-opacity')) {
            elementData.element.setAttribute('opacity', value);
            elementData.opacity = value;
        }
        
        this.updateCurrentSVG();
    }

    toggleElementControls(e) {
        const button = e.currentTarget;
        const targetId = button.dataset.target;
        const controls = document.getElementById(targetId);
        const isExpanded = controls.style.display === 'none';

        controls.style.display = isExpanded ? 'block' : 'none';
        button.style.transform = isExpanded ? 'rotate(180deg)' : 'rotate(0deg)';
    }

    applyPresetColor(e) {
        const color = e.target.dataset.color;
        // Apply to the first detected color or first element
        if (this.detectedColors.size > 0) {
            const firstColor = Array.from(this.detectedColors)[0];
            this.replaceColorInSVG(firstColor, color);
            
            // Update the color input
            const colorInput = document.querySelector(`input[data-original="${firstColor}"]`);
            if (colorInput) {
                colorInput.value = color;
            }
        } else if (this.svgElements.length > 0) {
            const firstElement = this.svgElements[0];
            firstElement.element.setAttribute('fill', color);
            firstElement.fill = color;
            this.updateCurrentSVG();
            
            // Update the element color input
            const elementInput = document.querySelector('.element-fill[data-element="0"]');
            if (elementInput) {
                elementInput.value = color;
            }
        }
    }

    replaceColorInSVG(oldColor, newColor) {
        if (!this.currentSVG) return;
        
        // Replace color in the SVG string
        const regex = new RegExp(oldColor.replace(/[.*+?^${}()|[\]\\]/g, '\\$&'), 'gi');
        this.currentSVG = this.currentSVG.replace(regex, newColor);
        
        // Update preview
        this.updatePreview(this.currentSVG);
        
        // Update text input
        document.getElementById('svgInput').value = this.currentSVG;
        
        // Update detected colors
        this.detectedColors.delete(oldColor);
        this.detectedColors.add(newColor);
    }

    updateCurrentSVG() {
        const previewContainer = document.getElementById('previewContainer');
        const svgElement = previewContainer.querySelector('svg');
        
        if (svgElement) {
            this.currentSVG = svgElement.outerHTML;
            document.getElementById('svgInput').value = this.currentSVG;
        }
    }

    randomizeColors() {
        if (this.detectedColors.size === 0 && this.svgElements.length === 0) {
            this.showError('No SVG loaded to randomize colors.');
            return;
        }
        
        // Randomize detected colors
        Array.from(this.detectedColors).forEach(color => {
            const randomColor = this.colorPresets[Math.floor(Math.random() * this.colorPresets.length)];
            this.replaceColorInSVG(color, randomColor);
        });
        
        // Randomize element colors
        this.svgElements.forEach((elementData, index) => {
            if (elementData.fill !== 'none') {
                const randomFill = this.colorPresets[Math.floor(Math.random() * this.colorPresets.length)];
                elementData.element.setAttribute('fill', randomFill);
                elementData.fill = randomFill;
            }
            
            if (elementData.stroke !== 'none') {
                const randomStroke = this.colorPresets[Math.floor(Math.random() * this.colorPresets.length)];
                elementData.element.setAttribute('stroke', randomStroke);
                elementData.stroke = randomStroke;
            }
        });
        
        this.updateCurrentSVG();
        this.detectColors(document.getElementById('previewContainer').querySelector('svg'));
        this.updateControlsPanel();
    }

    loadSampleSVG() {
        const sampleSVG = `<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 200 200" width="200" height="200">
    <defs>
        <linearGradient id="grad1" x1="0%" y1="0%" x2="100%" y2="100%">
            <stop offset="0%" style="stop-color:#6366f1;stop-opacity:1" />
            <stop offset="100%" style="stop-color:#8b5cf6;stop-opacity:1" />
        </linearGradient>
    </defs>
    <circle cx="100" cy="100" r="80" fill="url(#grad1)" stroke="#334155" stroke-width="3"/>
    <rect x="70" y="70" width="60" height="60" fill="#f59e0b" stroke="#dc2626" stroke-width="2" rx="10"/>
    <polygon points="100,50 120,90 80,90" fill="#10b981" stroke="#065f46" stroke-width="2"/>
    <text x="100" y="160" text-anchor="middle" fill="#1f2937" font-family="Arial" font-size="16" font-weight="bold">Sample SVG</text>
</svg>`;
        
        document.getElementById('svgInput').value = sampleSVG;
        this.processSVG(sampleSVG);
        this.switchTab('text');
    }

    clearInput() {
        document.getElementById('svgInput').value = '';
        document.getElementById('fileInput').value = '';
        this.clearPreview();
    }

    zoomPreview(factor) {
        const previewContainer = document.getElementById('previewContainer');
        const svgElement = previewContainer.querySelector('svg');
        
        if (svgElement) {
            const currentScale = parseFloat(svgElement.dataset.scale || 1);
            const newScale = currentScale * factor;
            
            svgElement.style.transform = `scale(${newScale})`;
            svgElement.dataset.scale = newScale;
        }
    }

    resetZoom() {
        const previewContainer = document.getElementById('previewContainer');
        const svgElement = previewContainer.querySelector('svg');
        
        if (svgElement) {
            svgElement.style.transform = 'scale(1)';
            svgElement.dataset.scale = 1;
        }
    }

    showExportModal() {
        if (!this.currentSVG) {
            this.showError('No SVG to export. Please load an SVG first.');
            return;
        }
        
        const modal = document.getElementById('exportModal');
        const textarea = document.getElementById('exportTextarea');
        
        textarea.value = this.currentSVG;
        modal.classList.add('show');
    }

    hideExportModal() {
        const modal = document.getElementById('exportModal');
        modal.classList.remove('show');
    }

    downloadSVG() {
        if (!this.currentSVG) return;
        
        const blob = new Blob([this.currentSVG], { type: 'image/svg+xml' });
        const url = URL.createObjectURL(blob);
        const a = document.createElement('a');
        
        a.href = url;
        a.download = 'edited-svg.svg';
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);
        URL.revokeObjectURL(url);
        
        this.showSuccess('SVG file downloaded successfully!');
    }

    async copySVGToClipboard() {
        if (!this.currentSVG) return;
        
        try {
            await navigator.clipboard.writeText(this.currentSVG);
            this.showSuccess('SVG code copied to clipboard!');
        } catch (err) {
            // Fallback for older browsers
            const textarea = document.getElementById('exportTextarea');
            textarea.select();
            document.execCommand('copy');
            this.showSuccess('SVG code copied to clipboard!');
        }
    }

    showError(message) {
        this.showNotification(message, 'error');
    }

    showSuccess(message) {
        this.showNotification(message, 'success');
    }

    showNotification(message, type = 'info') {
        // Create notification element
        const notification = document.createElement('div');
        notification.className = `notification notification-${type}`;
        notification.textContent = message;
        
        // Add to page
        document.body.appendChild(notification);
        
        // Show notification
        setTimeout(() => notification.classList.add('show'), 100);
        
        // Remove notification
        setTimeout(() => {
            notification.classList.remove('show');
            setTimeout(() => document.body.removeChild(notification), 300);
        }, 3000);
    }
}

// Initialize the application when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    new SVGEditor();
});
