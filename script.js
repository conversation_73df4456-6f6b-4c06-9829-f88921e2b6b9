// SVG Editor Application
class SVGEditor {
    constructor() {
        this.currentSVG = null;
        this.svgElements = [];
        this.detectedColors = new Set();
        this.colorPresets = [
            '#6366f1', '#8b5cf6', '#ec4899', '#ef4444', '#f97316',
            '#f59e0b', '#eab308', '#84cc16', '#22c55e', '#10b981',
            '#06b6d4', '#0ea5e9', '#3b82f6', '#6366f1', '#8b5cf6',
            '#a855f7', '#d946ef', '#ec4899', '#f43f5e', '#ef4444',
            '#f97316', '#f59e0b', '#eab308', '#84cc16', '#22c55e'
        ];

        // Animation system
        this.animations = new Map(); // elementId -> animation config
        this.globalAnimation = null;
        this.animationPresets = this.initializeAnimationPresets();
        this.isAnimationPlaying = false;

        // Solo view system
        this.soloMode = false;
        this.soloElementIndex = -1;
        
        this.init();
    }

    init() {
        this.setupEventListeners();
        this.setupCookieConsent();
        this.loadSampleSVG();
    }

    initializeAnimationPresets() {
        return {
            // Transform Animations
            'bounce': {
                name: 'Bounce',
                type: 'transform',
                keyframes: [
                    { transform: 'translateY(0)', offset: 0 },
                    { transform: 'translateY(-20px)', offset: 0.5 },
                    { transform: 'translateY(0)', offset: 1 }
                ],
                duration: 1000,
                easing: 'ease-out',
                category: 'movement'
            },
            'shake': {
                name: 'Shake',
                type: 'transform',
                keyframes: [
                    { transform: 'translateX(0)', offset: 0 },
                    { transform: 'translateX(-10px)', offset: 0.1 },
                    { transform: 'translateX(10px)', offset: 0.2 },
                    { transform: 'translateX(-10px)', offset: 0.3 },
                    { transform: 'translateX(10px)', offset: 0.4 },
                    { transform: 'translateX(-10px)', offset: 0.5 },
                    { transform: 'translateX(10px)', offset: 0.6 },
                    { transform: 'translateX(-10px)', offset: 0.7 },
                    { transform: 'translateX(10px)', offset: 0.8 },
                    { transform: 'translateX(-10px)', offset: 0.9 },
                    { transform: 'translateX(0)', offset: 1 }
                ],
                duration: 800,
                easing: 'ease-in-out',
                category: 'movement'
            },
            'pulse': {
                name: 'Pulse',
                type: 'transform',
                keyframes: [
                    { transform: 'scale(1)', offset: 0 },
                    { transform: 'scale(1.1)', offset: 0.5 },
                    { transform: 'scale(1)', offset: 1 }
                ],
                duration: 1200,
                easing: 'ease-in-out',
                category: 'scale'
            },
            'heartbeat': {
                name: 'Heartbeat',
                type: 'transform',
                keyframes: [
                    { transform: 'scale(1)', offset: 0 },
                    { transform: 'scale(1.3)', offset: 0.14 },
                    { transform: 'scale(1)', offset: 0.28 },
                    { transform: 'scale(1.3)', offset: 0.42 },
                    { transform: 'scale(1)', offset: 0.7 }
                ],
                duration: 1300,
                easing: 'ease-in-out',
                category: 'scale'
            },
            'swing': {
                name: 'Swing',
                type: 'transform',
                keyframes: [
                    { transform: 'rotate(0deg)', offset: 0 },
                    { transform: 'rotate(15deg)', offset: 0.2 },
                    { transform: 'rotate(-10deg)', offset: 0.4 },
                    { transform: 'rotate(5deg)', offset: 0.6 },
                    { transform: 'rotate(-5deg)', offset: 0.8 },
                    { transform: 'rotate(0deg)', offset: 1 }
                ],
                duration: 1000,
                easing: 'ease-in-out',
                category: 'rotation'
            },
            'spin': {
                name: 'Spin',
                type: 'transform',
                keyframes: [
                    { transform: 'rotate(0deg)', offset: 0 },
                    { transform: 'rotate(360deg)', offset: 1 }
                ],
                duration: 2000,
                easing: 'linear',
                category: 'rotation'
            },
            'wobble': {
                name: 'Wobble',
                type: 'transform',
                keyframes: [
                    { transform: 'translateX(0%) rotate(0deg)', offset: 0 },
                    { transform: 'translateX(-25%) rotate(-5deg)', offset: 0.15 },
                    { transform: 'translateX(20%) rotate(3deg)', offset: 0.3 },
                    { transform: 'translateX(-15%) rotate(-3deg)', offset: 0.45 },
                    { transform: 'translateX(10%) rotate(2deg)', offset: 0.6 },
                    { transform: 'translateX(-5%) rotate(-1deg)', offset: 0.75 },
                    { transform: 'translateX(0%) rotate(0deg)', offset: 1 }
                ],
                duration: 1000,
                easing: 'ease-in-out',
                category: 'movement'
            },
            'flip': {
                name: 'Flip',
                type: 'transform',
                keyframes: [
                    { transform: 'perspective(400px) rotateY(0)', offset: 0 },
                    { transform: 'perspective(400px) rotateY(-180deg)', offset: 0.5 },
                    { transform: 'perspective(400px) rotateY(0)', offset: 1 }
                ],
                duration: 1500,
                easing: 'ease-in-out',
                category: 'rotation'
            },
            'slideInLeft': {
                name: 'Slide In Left',
                type: 'transform',
                keyframes: [
                    { transform: 'translateX(-100%)', opacity: 0, offset: 0 },
                    { transform: 'translateX(0)', opacity: 1, offset: 1 }
                ],
                duration: 800,
                easing: 'ease-out',
                category: 'entrance'
            },
            'slideInRight': {
                name: 'Slide In Right',
                type: 'transform',
                keyframes: [
                    { transform: 'translateX(100%)', opacity: 0, offset: 0 },
                    { transform: 'translateX(0)', opacity: 1, offset: 1 }
                ],
                duration: 800,
                easing: 'ease-out',
                category: 'entrance'
            },
            'slideInUp': {
                name: 'Slide In Up',
                type: 'transform',
                keyframes: [
                    { transform: 'translateY(100%)', opacity: 0, offset: 0 },
                    { transform: 'translateY(0)', opacity: 1, offset: 1 }
                ],
                duration: 800,
                easing: 'ease-out',
                category: 'entrance'
            },
            'slideInDown': {
                name: 'Slide In Down',
                type: 'transform',
                keyframes: [
                    { transform: 'translateY(-100%)', opacity: 0, offset: 0 },
                    { transform: 'translateY(0)', opacity: 1, offset: 1 }
                ],
                duration: 800,
                easing: 'ease-out',
                category: 'entrance'
            },
            'fadeIn': {
                name: 'Fade In',
                type: 'opacity',
                keyframes: [
                    { opacity: 0, offset: 0 },
                    { opacity: 1, offset: 1 }
                ],
                duration: 1000,
                easing: 'ease-in',
                category: 'fade'
            },
            'fadeOut': {
                name: 'Fade Out',
                type: 'opacity',
                keyframes: [
                    { opacity: 1, offset: 0 },
                    { opacity: 0, offset: 1 }
                ],
                duration: 1000,
                easing: 'ease-out',
                category: 'fade'
            },
            'zoomIn': {
                name: 'Zoom In',
                type: 'transform',
                keyframes: [
                    { transform: 'scale(0)', opacity: 0, offset: 0 },
                    { transform: 'scale(1)', opacity: 1, offset: 1 }
                ],
                duration: 800,
                easing: 'ease-out',
                category: 'scale'
            },
            'zoomOut': {
                name: 'Zoom Out',
                type: 'transform',
                keyframes: [
                    { transform: 'scale(1)', opacity: 1, offset: 0 },
                    { transform: 'scale(0)', opacity: 0, offset: 1 }
                ],
                duration: 800,
                easing: 'ease-in',
                category: 'scale'
            },
            'rubberBand': {
                name: 'Rubber Band',
                type: 'transform',
                keyframes: [
                    { transform: 'scale(1)', offset: 0 },
                    { transform: 'scale(1.25, 0.75)', offset: 0.3 },
                    { transform: 'scale(0.75, 1.25)', offset: 0.4 },
                    { transform: 'scale(1.15, 0.85)', offset: 0.5 },
                    { transform: 'scale(0.95, 1.05)', offset: 0.65 },
                    { transform: 'scale(1.05, 0.95)', offset: 0.75 },
                    { transform: 'scale(1)', offset: 1 }
                ],
                duration: 1000,
                easing: 'ease-out',
                category: 'scale'
            },
            'jello': {
                name: 'Jello',
                type: 'transform',
                keyframes: [
                    { transform: 'skewX(0deg) skewY(0deg)', offset: 0 },
                    { transform: 'skewX(-12.5deg) skewY(-12.5deg)', offset: 0.111 },
                    { transform: 'skewX(6.25deg) skewY(6.25deg)', offset: 0.222 },
                    { transform: 'skewX(-3.125deg) skewY(-3.125deg)', offset: 0.333 },
                    { transform: 'skewX(1.5625deg) skewY(1.5625deg)', offset: 0.444 },
                    { transform: 'skewX(-0.78125deg) skewY(-0.78125deg)', offset: 0.555 },
                    { transform: 'skewX(0.390625deg) skewY(0.390625deg)', offset: 0.666 },
                    { transform: 'skewX(-0.1953125deg) skewY(-0.1953125deg)', offset: 0.777 },
                    { transform: 'skewX(0deg) skewY(0deg)', offset: 1 }
                ],
                duration: 1000,
                easing: 'ease-in-out',
                category: 'special'
            },
            'flash': {
                name: 'Flash',
                type: 'opacity',
                keyframes: [
                    { opacity: 1, offset: 0 },
                    { opacity: 0, offset: 0.25 },
                    { opacity: 1, offset: 0.5 },
                    { opacity: 0, offset: 0.75 },
                    { opacity: 1, offset: 1 }
                ],
                duration: 1000,
                easing: 'ease-in-out',
                category: 'fade'
            }
        };
    }

    setupEventListeners() {
        // Tab switching
        document.querySelectorAll('.tab-btn').forEach(btn => {
            btn.addEventListener('click', (e) => this.switchTab(e.target.dataset.tab));
        });

        // SVG input
        const svgInput = document.getElementById('svgInput');
        svgInput.addEventListener('input', () => this.handleSVGInput(svgInput.value));

        // File upload
        const fileInput = document.getElementById('fileInput');
        const fileUploadArea = document.getElementById('fileUploadArea');
        
        fileInput.addEventListener('change', (e) => this.handleFileUpload(e.target.files[0]));
        fileUploadArea.addEventListener('click', () => fileInput.click());
        fileUploadArea.addEventListener('dragover', (e) => this.handleDragOver(e));
        fileUploadArea.addEventListener('drop', (e) => this.handleFileDrop(e));

        // Control buttons
        document.getElementById('loadSampleBtn').addEventListener('click', () => this.loadSampleSVG());
        document.getElementById('clearInputBtn').addEventListener('click', () => this.clearInput());
        document.getElementById('randomizeColorsBtn').addEventListener('click', () => this.randomizeColors());

        // Preview controls
        document.getElementById('zoomInBtn').addEventListener('click', () => this.zoomPreview(1.2));
        document.getElementById('zoomOutBtn').addEventListener('click', () => this.zoomPreview(0.8));
        document.getElementById('resetZoomBtn').addEventListener('click', () => this.resetZoom());

        // Export functionality
        document.getElementById('exportBtn').addEventListener('click', () => this.showExportModal());
        document.getElementById('closeExportModal').addEventListener('click', () => this.hideExportModal());
        document.getElementById('downloadSvgBtn').addEventListener('click', () => this.downloadSVG());
        document.getElementById('copySvgBtn').addEventListener('click', () => this.copySVGToClipboard());

        // Animation controls
        document.getElementById('randomizeAnimationsBtn').addEventListener('click', () => this.randomizeAllAnimations());
        document.getElementById('playAnimationPreviewBtn').addEventListener('click', () => this.playAnimations());
        document.getElementById('pauseAnimationPreviewBtn').addEventListener('click', () => this.pauseAnimations());
        document.getElementById('stopAnimationPreviewBtn').addEventListener('click', () => this.stopAnimations());
        document.getElementById('applyGlobalAnimationBtn').addEventListener('click', () => this.applyGlobalAnimation());
        document.getElementById('clearAllAnimationsBtn').addEventListener('click', () => this.clearAllAnimations());
        document.getElementById('randomizeAllAnimationsBtn').addEventListener('click', () => this.randomizeAllAnimations());

        // Solo view controls
        document.getElementById('soloViewBtn').addEventListener('click', () => this.toggleSoloMode());
        document.getElementById('showAllBtn').addEventListener('click', () => this.exitSoloMode());

        // Control tabs
        document.querySelectorAll('.control-tab-btn').forEach(btn => {
            btn.addEventListener('click', (e) => this.switchControlTab(e.target.dataset.tab));
        });

        // Modal close on backdrop click
        document.getElementById('exportModal').addEventListener('click', (e) => {
            if (e.target.id === 'exportModal') this.hideExportModal();
        });

        // Export type tabs
        document.querySelectorAll('.export-type-btn').forEach(btn => {
            btn.addEventListener('click', (e) => this.switchExportType(e.target.dataset.type));
        });

        // Animation preview
        document.getElementById('previewAnimationBtn').addEventListener('click', () => this.previewAnimationInModal());
    }

    setupCookieConsent() {
        const cookieConsent = document.getElementById('cookieConsent');
        const acceptBtn = document.getElementById('acceptCookies');
        
        // Check if cookies were already accepted
        if (!localStorage.getItem('cookiesAccepted')) {
            setTimeout(() => {
                cookieConsent.classList.add('show');
            }, 1000);
        }

        acceptBtn.addEventListener('click', () => {
            localStorage.setItem('cookiesAccepted', 'true');
            cookieConsent.classList.remove('show');
        });
    }

    switchTab(tabName) {
        // Update tab buttons
        document.querySelectorAll('.tab-btn').forEach(btn => {
            btn.classList.toggle('active', btn.dataset.tab === tabName);
        });

        // Update tab content
        document.querySelectorAll('.tab-content').forEach(content => {
            content.classList.toggle('active', content.id === `${tabName}Tab`);
        });
    }

    handleSVGInput(svgCode) {
        if (svgCode.trim()) {
            this.processSVG(svgCode);
        } else {
            this.clearPreview();
        }
    }

    handleFileUpload(file) {
        if (!file) return;

        if (file.type !== 'image/svg+xml' && !file.name.endsWith('.svg')) {
            this.showError('Please select a valid SVG file.');
            return;
        }

        const reader = new FileReader();
        reader.onload = (e) => {
            const svgCode = e.target.result;
            document.getElementById('svgInput').value = svgCode;
            this.processSVG(svgCode);
            this.switchTab('text'); // Switch to text tab to show the loaded code
        };
        reader.onerror = () => {
            this.showError('Error reading the file.');
        };
        reader.readAsText(file);
    }

    handleDragOver(e) {
        e.preventDefault();
        e.stopPropagation();
        e.currentTarget.classList.add('dragover');
    }

    handleFileDrop(e) {
        e.preventDefault();
        e.stopPropagation();
        e.currentTarget.classList.remove('dragover');
        
        const files = e.dataTransfer.files;
        if (files.length > 0) {
            this.handleFileUpload(files[0]);
        }
    }

    processSVG(svgCode) {
        try {
            // Clean and validate SVG
            const cleanSVG = this.cleanSVGCode(svgCode);
            
            // Create DOM parser to validate SVG
            const parser = new DOMParser();
            const doc = parser.parseFromString(cleanSVG, 'image/svg+xml');
            
            // Check for parsing errors
            const parserError = doc.querySelector('parsererror');
            if (parserError) {
                throw new Error('Invalid SVG format');
            }

            const svgElement = doc.querySelector('svg');
            if (!svgElement) {
                throw new Error('No SVG element found');
            }

            this.currentSVG = cleanSVG;
            this.updatePreview(cleanSVG);
            this.parseSVGElements(svgElement);
            this.detectColors(svgElement);
            this.updateControlsPanel();
            
        } catch (error) {
            this.showError(`Error processing SVG: ${error.message}`);
            this.clearPreview();
        }
    }

    cleanSVGCode(svgCode) {
        // Remove any XML declarations and DOCTYPE
        let cleaned = svgCode.replace(/<\?xml[^>]*\?>/gi, '');
        cleaned = cleaned.replace(/<!DOCTYPE[^>]*>/gi, '');
        cleaned = cleaned.trim();
        
        // Ensure SVG has proper namespace if missing
        if (cleaned.includes('<svg') && !cleaned.includes('xmlns=')) {
            cleaned = cleaned.replace('<svg', '<svg xmlns="http://www.w3.org/2000/svg"');
        }
        
        return cleaned;
    }

    updatePreview(svgCode) {
        const previewContainer = document.getElementById('previewContainer');
        previewContainer.innerHTML = svgCode;
        
        const svgElement = previewContainer.querySelector('svg');
        if (svgElement) {
            // Ensure SVG is responsive
            svgElement.style.maxWidth = '100%';
            svgElement.style.height = 'auto';
            
            // Add viewBox if missing
            if (!svgElement.getAttribute('viewBox') && svgElement.getAttribute('width') && svgElement.getAttribute('height')) {
                const width = svgElement.getAttribute('width');
                const height = svgElement.getAttribute('height');
                svgElement.setAttribute('viewBox', `0 0 ${width} ${height}`);
            }
        }
    }

    clearPreview() {
        const previewContainer = document.getElementById('previewContainer');
        previewContainer.innerHTML = `
            <div class="preview-placeholder">
                <span class="placeholder-icon">
                    <svg viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                        <rect width="18" height="18" x="3" y="3" rx="2" ry="2"/>
                        <circle cx="9" cy="9" r="2"/>
                        <path d="m21 15-3.086-3.086a2 2 0 0 0-2.828 0L6 21"/>
                    </svg>
                </span>
                <p>SVG preview will appear here</p>
            </div>
        `;
        this.currentSVG = null;
        this.svgElements = [];
        this.detectedColors.clear();
        this.updateControlsPanel();
    }

    parseSVGElements(svgElement) {
        this.svgElements = [];
        const elements = svgElement.querySelectorAll('*');
        let visualElementIndex = 0;

        elements.forEach((element, index) => {
            const tagName = element.tagName.toLowerCase();

            // Skip non-visual elements
            if (['defs', 'metadata', 'title', 'desc', 'style', 'script'].includes(tagName)) {
                return;
            }

            const elementData = {
                id: `element-${visualElementIndex}`,
                element: element, // Keep reference for now, but we'll use fresh lookups for modifications
                tagName: tagName,
                visualIndex: visualElementIndex, // Track the visual element index
                fill: element.getAttribute('fill') || 'none',
                stroke: element.getAttribute('stroke') || 'none',
                strokeWidth: element.getAttribute('stroke-width') || '1',
                opacity: element.getAttribute('opacity') || '1'
            };

            this.svgElements.push(elementData);
            visualElementIndex++;
        });
    }

    detectColors(svgElement) {
        this.detectedColors.clear();
        
        // Find all color attributes
        const colorAttributes = ['fill', 'stroke', 'stop-color', 'flood-color', 'lighting-color'];
        const elements = svgElement.querySelectorAll('*');
        
        elements.forEach(element => {
            colorAttributes.forEach(attr => {
                const value = element.getAttribute(attr) || element.style[attr];
                if (value && value !== 'none' && value !== 'transparent') {
                    // Extract color values (hex, rgb, hsl, named colors)
                    const colorMatch = value.match(/#[0-9a-f]{3,6}|rgb\([^)]+\)|hsl\([^)]+\)|[a-z]+/gi);
                    if (colorMatch) {
                        colorMatch.forEach(color => {
                            if (color.startsWith('#') || color.startsWith('rgb') || color.startsWith('hsl')) {
                                this.detectedColors.add(color);
                            }
                        });
                    }
                }
            });
        });
    }

    updateControlsPanel() {
        const controlsContent = document.getElementById('controlsContent');

        if (this.svgElements.length === 0) {
            controlsContent.innerHTML = `
                <div class="no-elements">
                    <p>Load an SVG to start editing</p>
                </div>
            `;
            return;
        }

        // Check which tab is active
        const activeTab = document.querySelector('.control-tab-btn.active')?.dataset.tab || 'styling';

        let html = '';

        if (activeTab === 'styling') {
            // Add detected colors section
            if (this.detectedColors.size > 0) {
                html += this.generateColorSection();
            }

            // Add elements section
            html += this.generateElementsSection();
        } else if (activeTab === 'animation') {
            // Add animation controls for elements
            html += this.generateAnimationSection();
        }

        controlsContent.innerHTML = html;
        this.attachControlEventListeners();
    }

    generateColorSection() {
        const colors = Array.from(this.detectedColors);
        
        let html = `
            <div class="control-section">
                <h4>Detected Colors</h4>
                <div class="color-grid">
        `;
        
        colors.forEach((color, index) => {
            html += `
                <div class="color-item">
                    <input type="color" id="color-${index}" value="${this.normalizeColor(color)}" data-original="${color}">
                    <label for="color-${index}">${color}</label>
                </div>
            `;
        });
        
        html += `
                </div>
                <div class="color-presets">
                    <h5>Color Presets</h5>
                    <div class="preset-grid">
        `;
        
        this.colorPresets.forEach((color, index) => {
            html += `
                <button class="preset-color" data-color="${color}" style="background-color: ${color}" title="${color}"></button>
            `;
        });
        
        html += `
                    </div>
                </div>
            </div>
        `;
        
        return html;
    }

    generateElementsSection() {
        let html = `
            <div class="control-section">
                <h4>SVG Elements (${this.svgElements.length})</h4>
                <div class="elements-list">
        `;
        
        this.svgElements.forEach((elementData, index) => {
            html += `
                <div class="element-item" data-element-id="${elementData.id}">
                    <div class="element-header">
                        <span class="element-tag">&lt;${elementData.tagName}&gt;</span>
                        <div class="element-header-actions">
                            <button class="btn btn-outline btn-sm solo-element-btn" data-element="${index}" title="Solo View">
                                <svg viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                    <path d="M1 12s4-8 11-8 11 8 11 8-4 8-11 8-11-8-11-8z"/>
                                    <circle cx="12" cy="12" r="3"/>
                                </svg>
                            </button>
                            <button class="element-toggle" data-target="controls-${index}">
                                <svg viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                    <path d="m6 9 6 6 6-6"/>
                                </svg>
                            </button>
                        </div>
                    </div>
                    <div class="element-controls" id="controls-${index}">
                        <div class="control-group">
                            <label>Fill Color:</label>
                            <input type="color" class="element-fill" value="${this.normalizeColor(elementData.fill)}" data-element="${index}">
                        </div>
                        <div class="control-group">
                            <label>Stroke Color:</label>
                            <input type="color" class="element-stroke" value="${this.normalizeColor(elementData.stroke)}" data-element="${index}">
                        </div>
                        <div class="control-group">
                            <label>Stroke Width:</label>
                            <input type="range" class="element-stroke-width" min="0" max="10" step="0.5" value="${elementData.strokeWidth}" data-element="${index}">
                            <span class="range-value">${elementData.strokeWidth}</span>
                        </div>
                        <div class="control-group">
                            <label>Opacity:</label>
                            <input type="range" class="element-opacity" min="0" max="1" step="0.1" value="${elementData.opacity}" data-element="${index}">
                            <span class="range-value">${elementData.opacity}</span>
                        </div>
                    </div>
                </div>
            `;
        });
        
        html += `
                </div>
            </div>
        `;
        
        return html;
    }

    generateAnimationSection() {
        let html = `
            <div class="control-section">
                <h4>Element Animations</h4>
                <div class="elements-list">
        `;

        this.svgElements.forEach((elementData, index) => {
            const currentAnimation = this.animations.get(`element-${index}`);

            html += `
                <div class="element-item" data-element-id="${elementData.id}">
                    <div class="element-header">
                        <span class="element-tag">&lt;${elementData.tagName}&gt;</span>
                        <div class="element-header-actions">
                            <button class="btn btn-outline btn-sm solo-element-btn" data-element="${index}" title="Solo View">
                                <svg viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                    <path d="M1 12s4-8 11-8 11 8 11 8-4 8-11 8-11-8-11-8z"/>
                                    <circle cx="12" cy="12" r="3"/>
                                </svg>
                            </button>
                            <button class="element-toggle" data-target="animation-controls-${index}">
                                <svg viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                    <path d="m6 9 6 6 6-6"/>
                                </svg>
                            </button>
                        </div>
                    </div>
                    <div class="element-controls element-animation-controls" id="animation-controls-${index}">
                        ${currentAnimation ? `
                            <div class="current-animation">
                                <strong>Current: ${currentAnimation.name}</strong>
                                <button class="btn btn-outline btn-sm" onclick="svgEditor.removeElementAnimation('element-${index}')">Remove</button>
                            </div>
                        ` : ''}

                        <div class="animation-categories">
                            ${this.generateAnimationPresetsByCategory(index)}
                        </div>

                        <div class="animation-settings">
                            <div class="setting-group">
                                <label>Duration (ms):</label>
                                <input type="number" class="element-duration" value="1000" min="100" max="10000" step="100" data-element="${index}">
                            </div>
                            <div class="setting-group">
                                <label>Delay (ms):</label>
                                <input type="number" class="element-delay" value="0" min="0" max="5000" step="100" data-element="${index}">
                            </div>
                            <div class="setting-group">
                                <label>Iterations:</label>
                                <select class="element-iterations" data-element="${index}">
                                    <option value="1">1</option>
                                    <option value="2">2</option>
                                    <option value="3">3</option>
                                    <option value="5">5</option>
                                    <option value="infinite">Infinite</option>
                                </select>
                            </div>
                            <div class="setting-group">
                                <label>Direction:</label>
                                <select class="element-direction" data-element="${index}">
                                    <option value="normal">Normal</option>
                                    <option value="reverse">Reverse</option>
                                    <option value="alternate">Alternate</option>
                                    <option value="alternate-reverse">Alternate Reverse</option>
                                </select>
                            </div>
                        </div>
                    </div>
                </div>
            `;
        });

        html += `
                </div>
            </div>
        `;

        return html;
    }

    generateAnimationPresetsByCategory(elementIndex) {
        // Group presets by category
        const categories = {};
        Object.entries(this.animationPresets).forEach(([key, preset]) => {
            if (!categories[preset.category]) {
                categories[preset.category] = [];
            }
            categories[preset.category].push({ key, preset });
        });

        let html = '';
        Object.entries(categories).forEach(([category, presets]) => {
            html += `
                <div class="animation-category">
                    <h5>${category.charAt(0).toUpperCase() + category.slice(1)}</h5>
                    <div class="animation-preset-grid">
            `;

            presets.forEach(({ key, preset }) => {
                html += `
                    <button class="animation-preset-btn" data-preset="${key}" data-element="${elementIndex}">
                        ${preset.name}
                    </button>
                `;
            });

            html += `
                    </div>
                </div>
            `;
        });

        return html;
    }

    normalizeColor(color) {
        if (!color || color === 'none' || color === 'transparent') {
            return '#000000';
        }

        // If it's already a hex color, return it
        if (color.startsWith('#')) {
            // Convert #abc to #aabbcc
            if (color.length === 4) {
                return '#' + color[1] + color[1] + color[2] + color[2] + color[3] + color[3];
            }
            return color;
        }

        // Handle url() references (gradients, etc.)
        if (color.startsWith('url(')) {
            return '#000000';
        }

        try {
            // For other color formats, we'll use a temporary element to get computed color
            const tempElement = document.createElement('div');
            tempElement.style.color = color;
            document.body.appendChild(tempElement);
            const computedColor = window.getComputedStyle(tempElement).color;
            document.body.removeChild(tempElement);

            // Convert rgb to hex
            const rgbMatch = computedColor.match(/rgb\((\d+),\s*(\d+),\s*(\d+)\)/);
            if (rgbMatch) {
                const r = parseInt(rgbMatch[1]).toString(16).padStart(2, '0');
                const g = parseInt(rgbMatch[2]).toString(16).padStart(2, '0');
                const b = parseInt(rgbMatch[3]).toString(16).padStart(2, '0');
                return `#${r}${g}${b}`;
            }
        } catch (error) {
            console.warn('Error normalizing color:', color, error);
        }

        return '#000000';
    }

    attachControlEventListeners() {
        // Color change listeners
        document.querySelectorAll('input[type="color"]').forEach(input => {
            input.addEventListener('change', (e) => this.handleColorChange(e));
        });

        // Range input listeners
        document.querySelectorAll('input[type="range"]').forEach(input => {
            input.addEventListener('input', (e) => this.handleRangeChange(e));
        });

        // Element toggle listeners
        document.querySelectorAll('.element-toggle').forEach(button => {
            button.addEventListener('click', (e) => this.toggleElementControls(e));
        });

        // Preset color listeners
        document.querySelectorAll('.preset-color').forEach(button => {
            button.addEventListener('click', (e) => this.applyPresetColor(e));
        });

        // Animation preset listeners
        document.querySelectorAll('.animation-preset-btn').forEach(button => {
            button.addEventListener('click', (e) => this.handleAnimationPresetClick(e));
        });

        // Solo view element buttons
        document.querySelectorAll('.solo-element-btn').forEach(button => {
            button.addEventListener('click', (e) => {
                const elementIndex = parseInt(e.target.closest('.solo-element-btn').dataset.element);
                this.setSoloElement(elementIndex);
            });
        });
    }

    handleColorChange(e) {
        const input = e.target;
        const newColor = input.value;

        try {
            if (input.dataset.original) {
                // This is a detected color input
                this.replaceColorInSVG(input.dataset.original, newColor);
            } else if (input.dataset.element !== undefined) {
                // This is an element-specific color input
                const elementIndex = parseInt(input.dataset.element);
                if (isNaN(elementIndex) || elementIndex < 0 || elementIndex >= this.svgElements.length) {
                    console.error('Invalid element index:', elementIndex);
                    return;
                }
                this.updateElementAttribute(elementIndex, input, newColor);
            }
        } catch (error) {
            console.error('Error in handleColorChange:', error);
            this.showError('Error updating color: ' + error.message);
        }
    }

    handleRangeChange(e) {
        const input = e.target;
        const value = input.value;
        const elementIndex = parseInt(input.dataset.element);

        try {
            // Update the display value
            const valueSpan = input.nextElementSibling;
            if (valueSpan && valueSpan.classList.contains('range-value')) {
                valueSpan.textContent = value;
            }

            if (isNaN(elementIndex) || elementIndex < 0 || elementIndex >= this.svgElements.length) {
                console.error('Invalid element index:', elementIndex);
                return;
            }

            this.updateElementAttribute(elementIndex, input, value);
        } catch (error) {
            console.error('Error in handleRangeChange:', error);
            this.showError('Error updating attribute: ' + error.message);
        }
    }

    toggleElementControls(e) {
        const button = e.currentTarget;
        const targetId = button.dataset.target;
        const controls = document.getElementById(targetId);
        const isExpanded = controls.style.display === 'none';

        controls.style.display = isExpanded ? 'block' : 'none';
        button.style.transform = isExpanded ? 'rotate(180deg)' : 'rotate(0deg)';
    }

    applyPresetColor(e) {
        const color = e.target.dataset.color;
        // Apply to the first detected color or first element
        if (this.detectedColors.size > 0) {
            const firstColor = Array.from(this.detectedColors)[0];
            this.replaceColorInSVG(firstColor, color);

            // Update the color input
            const colorInput = document.querySelector(`input[data-original="${firstColor}"]`);
            if (colorInput) {
                colorInput.value = color;
            }
        } else if (this.svgElements.length > 0) {
            // Use the new method to update the first element
            const mockInput = {
                classList: { contains: (cls) => cls === 'element-fill' },
                dataset: { element: '0' }
            };
            this.updateElementAttribute(0, mockInput, color);

            // Update the element color input
            const elementInput = document.querySelector('.element-fill[data-element="0"]');
            if (elementInput) {
                elementInput.value = color;
            }
        }
    }

    replaceColorInSVG(oldColor, newColor) {
        if (!this.currentSVG) return;
        
        // Replace color in the SVG string
        const regex = new RegExp(oldColor.replace(/[.*+?^${}()|[\]\\]/g, '\\$&'), 'gi');
        this.currentSVG = this.currentSVG.replace(regex, newColor);
        
        // Update preview
        this.updatePreview(this.currentSVG);
        
        // Update text input
        document.getElementById('svgInput').value = this.currentSVG;
        
        // Update detected colors
        this.detectedColors.delete(oldColor);
        this.detectedColors.add(newColor);
    }

    updateElementAttribute(elementIndex, input, value) {
        // Get fresh reference to the SVG element in the preview
        const previewContainer = document.getElementById('previewContainer');
        const svgElement = previewContainer.querySelector('svg');

        if (!svgElement) {
            console.error('No SVG element found in preview');
            this.showError('No SVG element found in preview');
            return;
        }

        // Get all elements again to ensure fresh references
        const allElements = svgElement.querySelectorAll('*');
        let targetElement = null;
        let currentIndex = 0;

        // Find the target element by matching the index of visual elements
        for (let element of allElements) {
            const tagName = element.tagName.toLowerCase();
            // Skip non-visual elements (same logic as parseSVGElements)
            if (['defs', 'metadata', 'title', 'desc', 'style', 'script'].includes(tagName)) {
                continue;
            }

            if (currentIndex === elementIndex) {
                targetElement = element;
                break;
            }
            currentIndex++;
        }

        if (!targetElement) {
            console.error(`Element at index ${elementIndex} not found`);
            this.showError(`Element at index ${elementIndex} not found`);
            return;
        }

        // Update the attribute based on input type
        if (input.classList.contains('element-fill')) {
            targetElement.setAttribute('fill', value);
            // Update our stored data
            if (this.svgElements[elementIndex]) {
                this.svgElements[elementIndex].fill = value;
            }
        } else if (input.classList.contains('element-stroke')) {
            targetElement.setAttribute('stroke', value);
            if (this.svgElements[elementIndex]) {
                this.svgElements[elementIndex].stroke = value;
            }
        } else if (input.classList.contains('element-stroke-width')) {
            targetElement.setAttribute('stroke-width', value);
            if (this.svgElements[elementIndex]) {
                this.svgElements[elementIndex].strokeWidth = value;
            }
        } else if (input.classList.contains('element-opacity')) {
            targetElement.setAttribute('opacity', value);
            if (this.svgElements[elementIndex]) {
                this.svgElements[elementIndex].opacity = value;
            }
        }

        // Update the current SVG string
        this.updateCurrentSVG();
    }

    updateCurrentSVG() {
        const previewContainer = document.getElementById('previewContainer');
        const svgElement = previewContainer.querySelector('svg');

        if (svgElement) {
            this.currentSVG = svgElement.outerHTML;
            document.getElementById('svgInput').value = this.currentSVG;
        }
    }

    randomizeColors() {
        if (this.detectedColors.size === 0 && this.svgElements.length === 0) {
            this.showError('No SVG loaded to randomize colors.');
            return;
        }

        const previewContainer = document.getElementById('previewContainer');
        const svgElement = previewContainer.querySelector('svg');

        if (!svgElement) {
            this.showError('No SVG element found.');
            return;
        }

        // Randomize detected colors first
        Array.from(this.detectedColors).forEach(color => {
            const randomColor = this.colorPresets[Math.floor(Math.random() * this.colorPresets.length)];
            this.replaceColorInSVG(color, randomColor);
        });

        // Randomize element colors using fresh element references
        const allElements = svgElement.querySelectorAll('*');
        let visualIndex = 0;

        allElements.forEach(element => {
            const tagName = element.tagName.toLowerCase();
            // Skip non-visual elements
            if (['defs', 'metadata', 'title', 'desc', 'style', 'script'].includes(tagName)) {
                return;
            }

            const currentFill = element.getAttribute('fill');
            const currentStroke = element.getAttribute('stroke');

            if (currentFill && currentFill !== 'none' && !currentFill.startsWith('url(')) {
                const randomFill = this.colorPresets[Math.floor(Math.random() * this.colorPresets.length)];
                element.setAttribute('fill', randomFill);
                if (this.svgElements[visualIndex]) {
                    this.svgElements[visualIndex].fill = randomFill;
                }
            }

            if (currentStroke && currentStroke !== 'none' && !currentStroke.startsWith('url(')) {
                const randomStroke = this.colorPresets[Math.floor(Math.random() * this.colorPresets.length)];
                element.setAttribute('stroke', randomStroke);
                if (this.svgElements[visualIndex]) {
                    this.svgElements[visualIndex].stroke = randomStroke;
                }
            }

            visualIndex++;
        });

        this.updateCurrentSVG();
        this.detectColors(svgElement);
        this.updateControlsPanel();
    }

    loadSampleSVG() {
        const sampleSVG = `<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 200 200" width="200" height="200">
    <defs>
        <linearGradient id="grad1" x1="0%" y1="0%" x2="100%" y2="100%">
            <stop offset="0%" style="stop-color:#6366f1;stop-opacity:1" />
            <stop offset="100%" style="stop-color:#8b5cf6;stop-opacity:1" />
        </linearGradient>
    </defs>
    <circle cx="100" cy="100" r="80" fill="url(#grad1)" stroke="#334155" stroke-width="3"/>
    <rect x="70" y="70" width="60" height="60" fill="#f59e0b" stroke="#dc2626" stroke-width="2" rx="10"/>
    <polygon points="100,50 120,90 80,90" fill="#10b981" stroke="#065f46" stroke-width="2"/>
    <text x="100" y="160" text-anchor="middle" fill="#1f2937" font-family="Arial" font-size="16" font-weight="bold">Sample SVG</text>
</svg>`;
        
        document.getElementById('svgInput').value = sampleSVG;
        this.processSVG(sampleSVG);
        this.switchTab('text');
    }

    clearInput() {
        document.getElementById('svgInput').value = '';
        document.getElementById('fileInput').value = '';
        this.clearPreview();
    }

    zoomPreview(factor) {
        const previewContainer = document.getElementById('previewContainer');
        const svgElement = previewContainer.querySelector('svg');
        
        if (svgElement) {
            const currentScale = parseFloat(svgElement.dataset.scale || 1);
            const newScale = currentScale * factor;
            
            svgElement.style.transform = `scale(${newScale})`;
            svgElement.dataset.scale = newScale;
        }
    }

    resetZoom() {
        const previewContainer = document.getElementById('previewContainer');
        const svgElement = previewContainer.querySelector('svg');
        
        if (svgElement) {
            svgElement.style.transform = 'scale(1)';
            svgElement.dataset.scale = 1;
        }
    }

    showExportModal() {
        if (!this.currentSVG) {
            this.showError('No SVG to export. Please load an SVG first.');
            return;
        }

        const modal = document.getElementById('exportModal');

        // Reset to static export by default
        this.switchExportType('static');

        modal.classList.add('show');
    }

    switchExportType(type) {
        // Update tab buttons
        document.querySelectorAll('.export-type-btn').forEach(btn => {
            btn.classList.toggle('active', btn.dataset.type === type);
        });

        // Show/hide animation settings
        const animationSettings = document.getElementById('animationExportSettings');
        const previewBtn = document.getElementById('previewAnimationBtn');
        const textarea = document.getElementById('exportTextarea');

        if (type === 'animated') {
            animationSettings.style.display = 'block';
            previewBtn.style.display = 'inline-flex';

            // Generate animated SVG
            textarea.value = this.generateAnimatedSVG();
        } else {
            animationSettings.style.display = 'none';
            previewBtn.style.display = 'none';

            // Show static SVG
            textarea.value = this.currentSVG;
        }
    }

    generateAnimatedSVG() {
        if (this.animations.size === 0 && !this.globalAnimation) {
            return this.currentSVG;
        }

        const includeCSS = document.getElementById('includeCSS')?.checked ?? true;
        const includeJS = document.getElementById('includeJS')?.checked ?? false;
        const autoPlay = document.getElementById('autoPlay')?.checked ?? true;

        let animatedSVG = this.currentSVG;

        // Parse the SVG to add animations
        const parser = new DOMParser();
        const doc = parser.parseFromString(animatedSVG, 'image/svg+xml');
        const svgElement = doc.querySelector('svg');

        if (!svgElement) return animatedSVG;

        // Add CSS animations if requested
        if (includeCSS) {
            const styleElement = doc.createElement('style');
            let css = '';

            // Generate CSS for global animation
            if (this.globalAnimation) {
                css += this.generateCSSAnimation('global-animation', this.globalAnimation);
                svgElement.classList.add('global-animation');
            }

            // Generate CSS for element animations
            this.animations.forEach((animation, elementId) => {
                const className = `animation-${elementId}`;
                css += this.generateCSSAnimation(className, animation);

                // Find and add class to element
                const elementIndex = parseInt(elementId.replace('element-', ''));
                const targetElement = this.getElementByVisualIndexInDoc(doc, elementIndex);
                if (targetElement) {
                    targetElement.classList.add(className);
                }
            });

            styleElement.textContent = css;
            svgElement.insertBefore(styleElement, svgElement.firstChild);
        }

        // Add JavaScript controls if requested
        if (includeJS) {
            const scriptElement = doc.createElement('script');
            scriptElement.textContent = this.generateAnimationScript(autoPlay);
            svgElement.appendChild(scriptElement);
        }

        return new XMLSerializer().serializeToString(doc);
    }

    generateCSSAnimation(className, animation) {
        let keyframes = '@keyframes ' + className + ' {\n';

        animation.keyframes.forEach(frame => {
            const percent = (frame.offset * 100).toFixed(1);
            keyframes += `  ${percent}% {\n`;

            if (frame.transform) {
                keyframes += `    transform: ${frame.transform};\n`;
            }
            if (frame.opacity !== undefined) {
                keyframes += `    opacity: ${frame.opacity};\n`;
            }

            keyframes += '  }\n';
        });

        keyframes += '}\n\n';

        // Add animation class
        keyframes += `.${className} {\n`;
        keyframes += `  animation: ${className} ${animation.duration}ms ${animation.easing}`;

        if (animation.delay) {
            keyframes += ` ${animation.delay}ms`;
        }

        if (animation.iterations !== 1) {
            keyframes += ` ${animation.iterations}`;
        }

        if (animation.direction !== 'normal') {
            keyframes += ` ${animation.direction}`;
        }

        keyframes += ' both;\n';
        keyframes += '}\n\n';

        return keyframes;
    }

    generateAnimationScript(autoPlay) {
        return `
// SVG Animation Controls
(function() {
    const svg = document.currentScript.parentElement;

    function playAnimations() {
        svg.style.animationPlayState = 'running';
        svg.querySelectorAll('[class*="animation-"]').forEach(el => {
            el.style.animationPlayState = 'running';
        });
    }

    function pauseAnimations() {
        svg.style.animationPlayState = 'paused';
        svg.querySelectorAll('[class*="animation-"]').forEach(el => {
            el.style.animationPlayState = 'paused';
        });
    }

    function restartAnimations() {
        svg.style.animation = 'none';
        svg.querySelectorAll('[class*="animation-"]').forEach(el => {
            el.style.animation = 'none';
        });

        setTimeout(() => {
            svg.style.animation = '';
            svg.querySelectorAll('[class*="animation-"]').forEach(el => {
                el.style.animation = '';
            });
        }, 10);
    }

    // Add control methods to SVG element
    svg.playAnimations = playAnimations;
    svg.pauseAnimations = pauseAnimations;
    svg.restartAnimations = restartAnimations;

    ${autoPlay ? 'playAnimations();' : ''}
})();
        `.trim();
    }

    getElementByVisualIndexInDoc(doc, visualIndex) {
        const svgElement = doc.querySelector('svg');
        if (!svgElement) return null;

        const allElements = svgElement.querySelectorAll('*');
        let currentIndex = 0;

        for (let element of allElements) {
            const tagName = element.tagName.toLowerCase();
            // Skip non-visual elements
            if (['defs', 'metadata', 'title', 'desc', 'style', 'script'].includes(tagName)) {
                continue;
            }

            if (currentIndex === visualIndex) {
                return element;
            }
            currentIndex++;
        }

        return null;
    }

    previewAnimationInModal() {
        // Create a preview window
        const previewWindow = window.open('', 'AnimationPreview', 'width=800,height=600');
        const animatedSVG = this.generateAnimatedSVG();

        previewWindow.document.write(`
            <!DOCTYPE html>
            <html>
            <head>
                <title>Animation Preview</title>
                <style>
                    body {
                        margin: 0;
                        padding: 20px;
                        display: flex;
                        justify-content: center;
                        align-items: center;
                        min-height: 100vh;
                        background: #f0f0f0;
                    }
                    svg {
                        max-width: 100%;
                        max-height: 100%;
                        border: 1px solid #ccc;
                        background: white;
                    }
                </style>
            </head>
            <body>
                ${animatedSVG}
            </body>
            </html>
        `);

        previewWindow.document.close();
    }

    hideExportModal() {
        const modal = document.getElementById('exportModal');
        modal.classList.remove('show');
    }

    downloadSVG() {
        const textarea = document.getElementById('exportTextarea');
        const svgContent = textarea.value;

        if (!svgContent) return;

        // Determine if it's animated
        const isAnimated = document.querySelector('.export-type-btn.active')?.dataset.type === 'animated';
        const filename = isAnimated ? 'animated-svg.svg' : 'edited-svg.svg';

        const blob = new Blob([svgContent], { type: 'image/svg+xml' });
        const url = URL.createObjectURL(blob);
        const a = document.createElement('a');

        a.href = url;
        a.download = filename;
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);
        URL.revokeObjectURL(url);

        this.showSuccess(`${isAnimated ? 'Animated ' : ''}SVG file downloaded successfully!`);
    }

    async copySVGToClipboard() {
        const textarea = document.getElementById('exportTextarea');
        const svgContent = textarea.value;

        if (!svgContent) return;

        try {
            await navigator.clipboard.writeText(svgContent);
            this.showSuccess('SVG code copied to clipboard!');
        } catch (err) {
            // Fallback for older browsers
            textarea.select();
            document.execCommand('copy');
            this.showSuccess('SVG code copied to clipboard!');
        }
    }

    showError(message) {
        this.showNotification(message, 'error');
    }

    showSuccess(message) {
        this.showNotification(message, 'success');
    }

    showNotification(message, type = 'info') {
        // Create notification element
        const notification = document.createElement('div');
        notification.className = `notification notification-${type}`;
        notification.textContent = message;

        // Add to page
        document.body.appendChild(notification);

        // Show notification
        setTimeout(() => notification.classList.add('show'), 100);

        // Remove notification
        setTimeout(() => {
            notification.classList.remove('show');
            setTimeout(() => document.body.removeChild(notification), 300);
        }, 3000);
    }

    // Animation System Methods
    switchControlTab(tabName) {
        // Update tab buttons
        document.querySelectorAll('.control-tab-btn').forEach(btn => {
            btn.classList.toggle('active', btn.dataset.tab === tabName);
        });

        // Show/hide animation section and playback controls
        const animationSection = document.getElementById('animationSection');
        const animationPlaybackPreview = document.getElementById('animationPlaybackPreview');

        if (tabName === 'animation') {
            animationSection.style.display = 'block';
            animationPlaybackPreview.style.display = 'flex';
            this.populateAnimationPresets();
            this.updateAnimationControls();
            this.updatePlaybackButtons();
        } else {
            animationSection.style.display = 'none';
            animationPlaybackPreview.style.display = 'none';
        }

        // Update the controls panel content
        this.updateControlsPanel();
    }

    populateAnimationPresets() {
        const globalSelect = document.getElementById('globalAnimationPreset');
        if (globalSelect.children.length > 1) return; // Already populated

        // Group presets by category
        const categories = {};
        Object.entries(this.animationPresets).forEach(([key, preset]) => {
            if (!categories[preset.category]) {
                categories[preset.category] = [];
            }
            categories[preset.category].push({ key, preset });
        });

        // Add options grouped by category
        Object.entries(categories).forEach(([category, presets]) => {
            const optgroup = document.createElement('optgroup');
            optgroup.label = category.charAt(0).toUpperCase() + category.slice(1);

            presets.forEach(({ key, preset }) => {
                const option = document.createElement('option');
                option.value = key;
                option.textContent = preset.name;
                optgroup.appendChild(option);
            });

            globalSelect.appendChild(optgroup);
        });
    }

    updateAnimationControls() {
        if (this.svgElements.length === 0) return;

        // Update timeline
        this.updateAnimationTimeline();
    }

    updateAnimationTimeline() {
        const timelineContent = document.getElementById('timelineContent');

        if (this.animations.size === 0 && !this.globalAnimation) {
            timelineContent.innerHTML = `
                <div class="timeline-placeholder">
                    <p>Add animations to see timeline</p>
                </div>
            `;
            return;
        }

        let html = '';

        // Global animation
        if (this.globalAnimation) {
            html += `
                <div class="timeline-item">
                    <div class="timeline-info">
                        <div class="timeline-element">Entire SVG</div>
                        <div class="timeline-animation">${this.globalAnimation.name}</div>
                        <div class="timeline-duration">${this.globalAnimation.duration}ms</div>
                    </div>
                    <div class="timeline-actions">
                        <button class="btn btn-outline btn-sm" onclick="svgEditor.removeGlobalAnimation()">Remove</button>
                    </div>
                </div>
            `;
        }

        // Element animations
        this.animations.forEach((animation, elementId) => {
            const elementIndex = parseInt(elementId.replace('element-', ''));
            const elementData = this.svgElements[elementIndex];
            if (elementData) {
                html += `
                    <div class="timeline-item">
                        <div class="timeline-info">
                            <div class="timeline-element">&lt;${elementData.tagName}&gt;</div>
                            <div class="timeline-animation">${animation.name}</div>
                            <div class="timeline-duration">${animation.duration}ms</div>
                        </div>
                        <div class="timeline-actions">
                            <button class="btn btn-outline btn-sm" onclick="svgEditor.removeElementAnimation('${elementId}')">Remove</button>
                        </div>
                    </div>
                `;
            }
        });

        timelineContent.innerHTML = html;
    }

    applyGlobalAnimation() {
        const presetKey = document.getElementById('globalAnimationPreset').value;
        if (!presetKey) {
            this.showError('Please select an animation preset.');
            return;
        }

        const preset = this.animationPresets[presetKey];
        const duration = parseInt(document.getElementById('globalDuration').value);
        const delay = parseInt(document.getElementById('globalDelay').value);
        const iterations = document.getElementById('globalIterations').value;
        const direction = document.getElementById('globalDirection').value;

        this.globalAnimation = {
            ...preset,
            duration: duration,
            delay: delay,
            iterations: iterations === 'infinite' ? 'infinite' : parseInt(iterations),
            direction: direction
        };

        this.updateAnimationTimeline();
        this.showSuccess(`Applied ${preset.name} animation to entire SVG`);
    }

    removeGlobalAnimation() {
        this.globalAnimation = null;
        this.updateAnimationTimeline();
        this.showSuccess('Removed global animation');
    }

    addElementAnimation(elementIndex, presetKey, customSettings = {}) {
        if (!this.animationPresets[presetKey]) {
            this.showError('Invalid animation preset');
            return;
        }

        const preset = this.animationPresets[presetKey];
        const elementId = `element-${elementIndex}`;

        const animation = {
            ...preset,
            duration: customSettings.duration || preset.duration,
            delay: customSettings.delay || 0,
            iterations: customSettings.iterations || 1,
            direction: customSettings.direction || 'normal',
            easing: customSettings.easing || preset.easing
        };

        this.animations.set(elementId, animation);
        this.updateAnimationTimeline();

        return animation;
    }

    removeElementAnimation(elementId) {
        this.animations.delete(elementId);
        this.updateAnimationTimeline();
        this.showSuccess('Removed element animation');
    }

    clearAllAnimations() {
        this.animations.clear();
        this.globalAnimation = null;
        this.updateAnimationTimeline();
        this.showSuccess('Cleared all animations');
    }

    randomizeAllAnimations() {
        if (this.svgElements.length === 0) {
            this.showError('No SVG elements to animate');
            return;
        }

        // Clear existing animations
        this.animations.clear();

        // Get random presets for each element
        const presetKeys = Object.keys(this.animationPresets);

        this.svgElements.forEach((elementData, index) => {
            const randomPresetKey = presetKeys[Math.floor(Math.random() * presetKeys.length)];
            const randomDuration = 500 + Math.random() * 2000; // 500ms to 2500ms
            const randomDelay = Math.random() * 1000; // 0 to 1000ms delay

            this.addElementAnimation(index, randomPresetKey, {
                duration: randomDuration,
                delay: randomDelay,
                iterations: Math.random() > 0.7 ? 'infinite' : 1
            });
        });

        // Sometimes add a global animation too
        if (Math.random() > 0.5) {
            const globalPresets = ['pulse', 'swing', 'bounce', 'heartbeat'];
            const randomGlobalPreset = globalPresets[Math.floor(Math.random() * globalPresets.length)];

            document.getElementById('globalAnimationPreset').value = randomGlobalPreset;
            document.getElementById('globalDuration').value = 1000 + Math.random() * 2000;
            document.getElementById('globalDelay').value = Math.random() * 500;
            document.getElementById('globalIterations').value = Math.random() > 0.6 ? 'infinite' : '3';

            this.applyGlobalAnimation();
        }

        this.showSuccess(`Applied random animations to ${this.svgElements.length} elements`);
    }

    playAnimations() {
        if (this.animations.size === 0 && !this.globalAnimation) {
            this.showError('No animations to play. Add some animations first.');
            return;
        }

        const previewContainer = document.getElementById('previewContainer');
        const svgElement = previewContainer.querySelector('svg');

        if (!svgElement) {
            this.showError('No SVG element found');
            return;
        }

        this.isAnimationPlaying = true;
        this.updatePlaybackButtons();

        // Apply global animation
        if (this.globalAnimation) {
            this.applyAnimationToElement(svgElement, this.globalAnimation, 'global');
        }

        // Apply element animations
        this.animations.forEach((animation, elementId) => {
            const elementIndex = parseInt(elementId.replace('element-', ''));
            const targetElement = this.getElementByVisualIndex(elementIndex);

            if (targetElement) {
                this.applyAnimationToElement(targetElement, animation, elementId);
            }
        });

        this.showSuccess('Animations started');
    }

    pauseAnimations() {
        const previewContainer = document.getElementById('previewContainer');
        const animatedElements = previewContainer.querySelectorAll('[data-animation-id]');

        animatedElements.forEach(element => {
            const animations = element.getAnimations();
            animations.forEach(anim => anim.pause());
        });

        this.isAnimationPlaying = false;
        this.updatePlaybackButtons();
        this.showSuccess('Animations paused');
    }

    stopAnimations() {
        const previewContainer = document.getElementById('previewContainer');
        const animatedElements = previewContainer.querySelectorAll('[data-animation-id]');

        animatedElements.forEach(element => {
            const animations = element.getAnimations();
            animations.forEach(anim => {
                anim.cancel();
            });
            element.removeAttribute('data-animation-id');
            element.style.transform = '';
            element.style.opacity = '';
        });

        this.isAnimationPlaying = false;
        this.updatePlaybackButtons();
        this.showSuccess('Animations stopped');
    }

    updatePlaybackButtons() {
        const playBtn = document.getElementById('playAnimationPreviewBtn');
        const pauseBtn = document.getElementById('pauseAnimationPreviewBtn');
        const stopBtn = document.getElementById('stopAnimationPreviewBtn');

        if (this.isAnimationPlaying) {
            playBtn.disabled = true;
            pauseBtn.disabled = false;
            stopBtn.disabled = false;
        } else {
            playBtn.disabled = false;
            pauseBtn.disabled = true;
            stopBtn.disabled = true;
        }
    }

    applyAnimationToElement(element, animation, animationId) {
        // Set animation ID for tracking
        element.setAttribute('data-animation-id', animationId);

        // Create keyframes based on animation type
        let keyframes = [];

        if (animation.type === 'transform') {
            keyframes = animation.keyframes.map(frame => ({
                transform: frame.transform,
                opacity: frame.opacity !== undefined ? frame.opacity : 1,
                offset: frame.offset
            }));
        } else if (animation.type === 'opacity') {
            keyframes = animation.keyframes.map(frame => ({
                opacity: frame.opacity,
                offset: frame.offset
            }));
        }

        // Animation options
        const options = {
            duration: animation.duration,
            delay: animation.delay || 0,
            iterations: animation.iterations,
            direction: animation.direction,
            easing: animation.easing,
            fill: 'both'
        };

        // Apply animation
        try {
            const webAnimation = element.animate(keyframes, options);

            // Handle animation completion
            webAnimation.addEventListener('finish', () => {
                if (animation.iterations !== 'infinite') {
                    element.removeAttribute('data-animation-id');
                }
            });

            return webAnimation;
        } catch (error) {
            console.error('Error applying animation:', error);
            this.showError('Error applying animation: ' + error.message);
        }
    }

    getElementByVisualIndex(visualIndex) {
        const previewContainer = document.getElementById('previewContainer');
        const svgElement = previewContainer.querySelector('svg');

        if (!svgElement) return null;

        const allElements = svgElement.querySelectorAll('*');
        let currentIndex = 0;

        for (let element of allElements) {
            const tagName = element.tagName.toLowerCase();
            // Skip non-visual elements
            if (['defs', 'metadata', 'title', 'desc', 'style', 'script'].includes(tagName)) {
                continue;
            }

            if (currentIndex === visualIndex) {
                return element;
            }
            currentIndex++;
        }

        return null;
    }

    handleAnimationPresetClick(e) {
        const button = e.target;
        const presetKey = button.dataset.preset;
        const elementIndex = parseInt(button.dataset.element);

        // Get custom settings from the form
        const durationInput = document.querySelector(`.element-duration[data-element="${elementIndex}"]`);
        const delayInput = document.querySelector(`.element-delay[data-element="${elementIndex}"]`);
        const iterationsSelect = document.querySelector(`.element-iterations[data-element="${elementIndex}"]`);
        const directionSelect = document.querySelector(`.element-direction[data-element="${elementIndex}"]`);

        const customSettings = {
            duration: durationInput ? parseInt(durationInput.value) : 1000,
            delay: delayInput ? parseInt(delayInput.value) : 0,
            iterations: iterationsSelect ? (iterationsSelect.value === 'infinite' ? 'infinite' : parseInt(iterationsSelect.value)) : 1,
            direction: directionSelect ? directionSelect.value : 'normal'
        };

        // Apply animation
        const animation = this.addElementAnimation(elementIndex, presetKey, customSettings);

        if (animation) {
            // Update button states
            const parentControls = button.closest('.element-animation-controls');
            parentControls.querySelectorAll('.animation-preset-btn').forEach(btn => {
                btn.classList.remove('active');
            });
            button.classList.add('active');

            // Refresh the controls to show current animation
            this.updateControlsPanel();

            this.showSuccess(`Applied ${animation.name} to element`);
        }
    }

    // Solo View Methods
    toggleSoloMode() {
        if (this.soloMode) {
            this.exitSoloMode();
        } else {
            this.enterSoloMode();
        }
    }

    enterSoloMode() {
        if (this.svgElements.length === 0) {
            this.showError('No SVG elements to view in solo mode');
            return;
        }

        this.soloMode = true;
        this.soloElementIndex = 0; // Start with first element
        this.updateSoloView();
        this.updateSoloControls();
        this.showSuccess('Solo view mode enabled. Use element controls to switch between elements.');
    }

    exitSoloMode() {
        this.soloMode = false;
        this.soloElementIndex = -1;
        this.showAllElements();
        this.updateSoloControls();
        this.showSuccess('Solo view mode disabled. All elements are now visible.');
    }

    updateSoloView() {
        if (!this.soloMode || this.soloElementIndex < 0) return;

        const previewContainer = document.getElementById('previewContainer');
        const svgElement = previewContainer.querySelector('svg');

        if (!svgElement) return;

        // Hide all elements first
        const allElements = svgElement.querySelectorAll('*');
        let visualIndex = 0;

        allElements.forEach(element => {
            const tagName = element.tagName.toLowerCase();
            // Skip non-visual elements
            if (['defs', 'metadata', 'title', 'desc', 'style', 'script'].includes(tagName)) {
                return;
            }

            // Show only the selected element
            if (visualIndex === this.soloElementIndex) {
                element.style.opacity = '1';
                element.style.display = '';
            } else {
                element.style.opacity = '0.1';
                element.style.pointerEvents = 'none';
            }

            visualIndex++;
        });
    }

    showAllElements() {
        const previewContainer = document.getElementById('previewContainer');
        const svgElement = previewContainer.querySelector('svg');

        if (!svgElement) return;

        // Reset all elements to visible
        const allElements = svgElement.querySelectorAll('*');
        allElements.forEach(element => {
            element.style.opacity = '';
            element.style.display = '';
            element.style.pointerEvents = '';
        });
    }

    setSoloElement(elementIndex) {
        if (!this.soloMode) {
            this.enterSoloMode();
        }

        this.soloElementIndex = elementIndex;
        this.updateSoloView();

        const elementData = this.svgElements[elementIndex];
        if (elementData) {
            this.showSuccess(`Solo viewing: <${elementData.tagName}> element`);
        }
    }

    updateSoloControls() {
        const soloBtn = document.getElementById('soloViewBtn');
        const showAllBtn = document.getElementById('showAllBtn');

        if (this.soloMode) {
            soloBtn.style.display = 'none';
            showAllBtn.style.display = 'inline-flex';
        } else {
            soloBtn.style.display = 'inline-flex';
            showAllBtn.style.display = 'none';
        }
    }
}

// Initialize the application when DOM is loaded
let svgEditor;
document.addEventListener('DOMContentLoaded', () => {
    svgEditor = new SVGEditor();
});
